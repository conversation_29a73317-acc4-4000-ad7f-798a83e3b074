================================================================================
PERCENTAGE CALCULATION MIGRATION REPORT
================================================================================

📊 SUMMARY:
   Files with issues: 4
   Total issues found: 18

📈 ISSUE BREAKDOWN:
   coingecko_extraction: 17 occurrences
   manual_calculation: 1 occurrences

📁 DETAILED BREAKDOWN:

🔍 handlers/discord/market/market_commands.py
------------------------------------------
   Line 485: coingecko_extraction
      Code: gainers = [coin for coin in top_coins if coin.get('price_change_percentage_24h', 0) > 0]
      Match: price_change_percentage_24h

   Line 486: coingecko_extraction
      Code: losers = [coin for coin in top_coins if coin.get('price_change_percentage_24h', 0) < 0]
      Match: price_change_percentage_24h

   Line 488: coingecko_extraction
      Code: gainers = sorted(gainers, key=lambda x: x.get('price_change_percentage_24h', 0), reverse=True)[:8]
      Match: price_change_percentage_24h

   Line 489: coingecko_extraction
      Code: losers = sorted(losers, key=lambda x: x.get('price_change_percentage_24h', 0))[:8]
      Match: price_change_percentage_24h

   Line 496: coingecko_extraction
      Code: change = coin.get('price_change_percentage_24h', 0)
      Match: price_change_percentage_24h

   Line 518: coingecko_extraction
      Code: change = coin.get('price_change_percentage_24h', 0)
      Match: price_change_percentage_24h

🔍 services/market/percentage_calculation_service.py
-------------------------------------------------
   Line 94: manual_calculation
      Code: percentage = ((current_price - base_price) / base_price) * 100
      Match: ((current_price - base_price) / base_price) * 100

   Line 184: coingecko_extraction
      Code: percentage = coin_data.get('price_change_percentage_24h', 0.0) or 0.0
      Match: price_change_percentage_24h

🔍 services/market/market_service.py
---------------------------------
   Line 389: coingecko_extraction
      Code: 'price_change_percentage_24h': coin.get('price_change_percentage_24h', 0),
      Match: price_change_percentage_24h

   Line 389: coingecko_extraction
      Code: 'price_change_percentage_24h': coin.get('price_change_percentage_24h', 0),
      Match: price_change_percentage_24h

   Line 719: coingecko_extraction
      Code: coingecko_data.get('price_change_percentage_24h', 0) or
      Match: price_change_percentage_24h

   Line 844: coingecko_extraction
      Code: 'price_change_percentage_24h': market_data.get('price_change_percentage_24h', 0) or 0,
      Match: price_change_percentage_24h

   Line 844: coingecko_extraction
      Code: 'price_change_percentage_24h': market_data.get('price_change_percentage_24h', 0) or 0,
      Match: price_change_percentage_24h

   Line 916: coingecko_extraction
      Code: 'price_change_percentage_24h': market_data.get('price_change_percentage_24h', 0) or 0,
      Match: price_change_percentage_24h

   Line 916: coingecko_extraction
      Code: 'price_change_percentage_24h': market_data.get('price_change_percentage_24h', 0) or 0,
      Match: price_change_percentage_24h

🔍 services/market/trading_time_service.py
---------------------------------------
   Line 271: coingecko_extraction
      Code: 'price_change_24h': coin.get('price_change_percentage_24h', 0)
      Match: price_change_percentage_24h

   Line 336: coingecko_extraction
      Code: 'price_change_24h': coin.get('price_change_percentage_24h', 0),
      Match: price_change_percentage_24h

   Line 367: coingecko_extraction
      Code: price_change_24h = coin.get('price_change_percentage_24h', 0)
      Match: price_change_percentage_24h

🎯 MIGRATION RECOMMENDATIONS:

1. Replace all ticker.get('percentage') calls with:
   percentage_service.extract_binance_percentage(ticker)

2. Replace all price_change_percentage_24h extractions with:
   percentage_service.extract_coingecko_percentage(coin_data)

3. Replace manual calculations with:
   percentage_service.calculate_percentage_change(current, base)

4. Update daily_open calculations to use rolling_24h method
