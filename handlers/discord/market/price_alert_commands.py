"""
Discord commands for managing price alerts
"""

import logging
import time
import discord
from discord.ext import commands
from discord import app_commands
from typing import List, Optional

from services.market.market_service import get_market_service
from services.market.price_alert_service import get_price_alert_service
from utils.ui_components import format_price_display
from services.core.symbol_service import normalize_symbol, is_valid_symbol

logger = logging.getLogger(__name__)

class PriceAlertCommands(commands.Cog):
    """Commands for managing price alerts"""
    
    def __init__(self, bot):
        self.bot = bot
        self.market_service = get_market_service()
        self.price_alert_service = get_price_alert_service()
    
    @app_commands.command(name="add_price_alert", description="Add a price alert for a cryptocurrency")
    @app_commands.describe(
        symbol="Symbol to alert on (e.g., BTC, ETH, SOL)",
        price="Target price to trigger alert"
    )
    async def add_price_alert(self, interaction: discord.Interaction, symbol: str, price: float):
        """Add a price alert for a specific symbol and price"""
        start_time = time.time()
        
        if not self.bot.check_cooldown(interaction.user.id, "add_price_alert", 5):
            remaining = self.bot.get_cooldown_remaining(interaction.user.id, "add_price_alert", 5)
            await interaction.response.send_message(
                f"⏰ Please wait {remaining:.1f} seconds before adding another price alert.",
                ephemeral=True
            )
            return
        
        await interaction.response.defer(ephemeral=True)
        
        try:
            # Normalize and validate symbol
            normalized_symbol = normalize_symbol(symbol.upper().strip())
            
            if not is_valid_symbol(normalized_symbol):
                await interaction.followup.send(
                    f"❌ Invalid symbol: {symbol}. Please use a valid trading pair (e.g., BTC, ETH, SOL).",
                    ephemeral=True
                )
                await self.bot.record_command_execution("add_price_alert", start_time, False)
                return
            
            # Validate price
            if price <= 0:
                await interaction.followup.send(
                    "❌ Price must be greater than zero.",
                    ephemeral=True
                )
                await self.bot.record_command_execution("add_price_alert", start_time, False)
                return
            
            # Check if symbol is in watchlist
            from utils.config import get_watchlist_symbols
            watchlist_symbols = get_watchlist_symbols()
            
            if normalized_symbol not in watchlist_symbols:
                await interaction.followup.send(
                    f"❌ Symbol {normalized_symbol} is not in your watchlist. "
                    f"Only watchlist symbols can have price alerts.",
                    ephemeral=True
                )
                await self.bot.record_command_execution("add_price_alert", start_time, False)
                return
            
            # Add price target
            success = await self.price_alert_service.add_price_target(normalized_symbol, price)
            
            if success:
                # Get current price for reference
                prices = await self.market_service.get_prices([normalized_symbol])
                current_price = prices.get(normalized_symbol, 0)
                
                # Calculate price difference using unified service
                if current_price > 0:
                    from services.market.percentage_calculation_service import get_percentage_service
                    percentage_service = get_percentage_service()

                    result = percentage_service.calculate_percentage_change(price, current_price)
                    if result.is_valid:
                        price_diff = result.value
                        direction = "above" if price > current_price else "below"
                        diff_text = f"({direction} current price by {abs(price_diff):.2f}%)"
                    else:
                        diff_text = ""
                else:
                    diff_text = ""
                
                await interaction.followup.send(
                    f"✅ Added price alert for {normalized_symbol} at {format_price_display(price)} {diff_text}",
                    ephemeral=True
                )
                
                # Save price targets
                await self.price_alert_service.save_price_targets()
                
                await self.bot.record_command_execution("add_price_alert", start_time, True)
                logger.info(f"Added price alert for {normalized_symbol} at {price} by {interaction.user}")
            else:
                await interaction.followup.send(
                    f"⚠️ Price alert for {normalized_symbol} at {format_price_display(price)} already exists.",
                    ephemeral=True
                )
                await self.bot.record_command_execution("add_price_alert", start_time, False)
        
        except Exception as e:
            logger.error(f"Error adding price alert: {e}")
            await interaction.followup.send(
                "❌ An error occurred while adding the price alert.",
                ephemeral=True
            )
            await self.bot.record_command_execution("add_price_alert", start_time, False)
    
    @app_commands.command(name="remove_price_alert", description="Remove a price alert")
    @app_commands.describe(
        symbol="Symbol to remove alert for (e.g., BTC, ETH, SOL)",
        price="Target price to remove (leave empty to show list)"
    )
    async def remove_price_alert(self, interaction: discord.Interaction, symbol: str, price: Optional[float] = None):
        """Remove a price alert for a specific symbol and price"""
        start_time = time.time()
        
        if not self.bot.check_cooldown(interaction.user.id, "remove_price_alert", 5):
            remaining = self.bot.get_cooldown_remaining(interaction.user.id, "remove_price_alert", 5)
            await interaction.response.send_message(
                f"⏰ Please wait {remaining:.1f} seconds before removing another price alert.",
                ephemeral=True
            )
            return
        
        await interaction.response.defer(ephemeral=True)
        
        try:
            # Normalize and validate symbol
            normalized_symbol = normalize_symbol(symbol.upper().strip())
            
            if not is_valid_symbol(normalized_symbol):
                await interaction.followup.send(
                    f"❌ Invalid symbol: {symbol}. Please use a valid trading pair (e.g., BTC, ETH, SOL).",
                    ephemeral=True
                )
                await self.bot.record_command_execution("remove_price_alert", start_time, False)
                return
            
            # Get price targets for this symbol
            targets = await self.price_alert_service.get_price_targets(normalized_symbol)
            symbol_targets = targets.get(normalized_symbol, [])
            
            if not symbol_targets:
                await interaction.followup.send(
                    f"❌ No price alerts configured for {normalized_symbol}.",
                    ephemeral=True
                )
                await self.bot.record_command_execution("remove_price_alert", start_time, False)
                return
            
            # If no price specified, show list of targets to remove
            if price is None:
                targets_list = "\n".join([f"• {format_price_display(p)}" for p in symbol_targets])
                
                await interaction.followup.send(
                    f"Please specify which price alert to remove for {normalized_symbol}:\n\n{targets_list}\n\n"
                    f"Use `/remove_price_alert {normalized_symbol} <price>` to remove a specific alert.",
                    ephemeral=True
                )
                await self.bot.record_command_execution("remove_price_alert", start_time, True)
                return
            
            # Remove the specified price target
            success = await self.price_alert_service.remove_price_target(normalized_symbol, price)
            
            if success:
                await interaction.followup.send(
                    f"✅ Removed price alert for {normalized_symbol} at {format_price_display(price)}",
                    ephemeral=True
                )
                
                # Save price targets
                await self.price_alert_service.save_price_targets()
                
                await self.bot.record_command_execution("remove_price_alert", start_time, True)
                logger.info(f"Removed price alert for {normalized_symbol} at {price} by {interaction.user}")
            else:
                await interaction.followup.send(
                    f"❌ No price alert found for {normalized_symbol} at {format_price_display(price)}.",
                    ephemeral=True
                )
                await self.bot.record_command_execution("remove_price_alert", start_time, False)
        
        except Exception as e:
            logger.error(f"Error removing price alert: {e}")
            await interaction.followup.send(
                "❌ An error occurred while removing the price alert.",
                ephemeral=True
            )
            await self.bot.record_command_execution("remove_price_alert", start_time, False)
    
    @app_commands.command(name="list_price_alerts", description="List all configured price alerts")
    async def list_price_alerts(self, interaction: discord.Interaction):
        """List all configured price alerts"""
        start_time = time.time()
        
        if not self.bot.check_cooldown(interaction.user.id, "list_price_alerts", 5):
            remaining = self.bot.get_cooldown_remaining(interaction.user.id, "list_price_alerts", 5)
            await interaction.response.send_message(
                f"⏰ Please wait {remaining:.1f} seconds before using this command again.",
                ephemeral=True
            )
            return
        
        await interaction.response.defer(ephemeral=True)
        
        try:
            # Get all price targets
            all_targets = await self.price_alert_service.get_price_targets()
            
            if not all_targets:
                await interaction.followup.send(
                    "❌ No price alerts configured.",
                    ephemeral=True
                )
                await self.bot.record_command_execution("list_price_alerts", start_time, False)
                return
            
            # Get current prices
            symbols = list(all_targets.keys())
            prices = await self.market_service.get_prices(symbols)
            
            # Create embed
            embed = discord.Embed(
                title="💰 Configured Price Alerts",
                description="Current price alerts for watchlist symbols:",
                color=0x00ff88,
                timestamp=discord.utils.utcnow()
            )
            
            # Add each symbol's targets
            for symbol, targets in all_targets.items():
                if not targets:
                    continue
                
                current_price = prices.get(symbol, 0)
                
                # Format targets with current price context
                targets_text = ""
                for target in sorted(targets, reverse=True):
                    if current_price > 0:
                        # Use unified percentage calculation service
                        from services.market.percentage_calculation_service import get_percentage_service
                        percentage_service = get_percentage_service()

                        result = percentage_service.calculate_percentage_change(target, current_price)
                        if result.is_valid:
                            diff_pct = result.value
                            if target > current_price:
                                targets_text += f"• {format_price_display(target)} 🔼 ({diff_pct:.2f}% above current)\n"
                            else:
                                targets_text += f"• {format_price_display(target)} 🔽 ({abs(diff_pct):.2f}% below current)\n"
                        else:
                            targets_text += f"• {format_price_display(target)}\n"
                    else:
                        targets_text += f"• {format_price_display(target)}\n"
                
                # Add field for this symbol
                token = symbol.replace('USDT', '')
                current_price_text = f"Current: {format_price_display(current_price)}" if current_price > 0 else "Current price unknown"
                
                embed.add_field(
                    name=f"{token} ({symbol})",
                    value=f"{current_price_text}\n{targets_text}",
                    inline=False
                )
            
            # Add footer
            embed.set_footer(text="ChartFix Price Alert System")
            
            await interaction.followup.send(embed=embed, ephemeral=True)
            await self.bot.record_command_execution("list_price_alerts", start_time, True)
            logger.info(f"Listed price alerts for {interaction.user}")
        
        except Exception as e:
            logger.error(f"Error listing price alerts: {e}")
            await interaction.followup.send(
                "❌ An error occurred while listing price alerts.",
                ephemeral=True
            )
            await self.bot.record_command_execution("list_price_alerts", start_time, False)

# Function to setup this cog
async def setup(bot):
    await bot.add_cog(PriceAlertCommands(bot)) 