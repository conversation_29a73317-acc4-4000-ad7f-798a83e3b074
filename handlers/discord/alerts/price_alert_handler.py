"""
Discord Price Al<PERSON> - Sends price alerts to Discord
"""

import logging
import discord
from typing import Dict, Any
from datetime import datetime, timezone
from utils.config import get_admin_id, get_guild_id, load_config
from utils.ui_components import format_price_display

logger = logging.getLogger(__name__)

class PriceAlertHandler:
    """Handler for sending price alerts to Discord"""

    def __init__(self, bot):
        self.bot = bot
        self.admin_id = get_admin_id()
        self.guild_id = get_guild_id()
        
        # Load channel name from config
        config = load_config()
        price_alerts_config = config.get('price_alerts', {})
        self.channel_name = price_alerts_config.get('channel_name', '🚨-alerts')

    async def handle_price_alert(self, alert_type: str, alert_data: Dict[str, Any]):
        """Process price alerts and send to Discord"""
        try:
            # Create appropriate embed based on alert type
            if alert_type == "price_target":
                embed = await self._create_price_target_embed(alert_data)
            elif alert_type == "daily_change":
                embed = await self._create_daily_change_embed(alert_data)
            elif alert_type == "short_term_price_change":
                embed = await self._create_short_term_change_embed(alert_data)
            else:
                logger.warning(f"Unknown price alert type: {alert_type}")
                return

            # Send to alerts channel in guild
            await self._send_to_alerts_channel(embed)

        except Exception as e:
            logger.error(f"Error handling price alert: {e}")
    
    async def _create_price_target_embed(self, alert_data: Dict[str, Any]) -> discord.Embed:
        """Create embed for price target alerts"""
        symbol = alert_data.get('symbol', 'UNKNOWN')
        token = symbol.replace('USDT', '')
        current_price = alert_data.get('current_price', 0.0)
        target_price = alert_data.get('target_price', 0.0)
        direction = alert_data.get('direction', 'crossed')
        pct_change = alert_data.get('percentage_change', 0.0)
        
        # Format direction with emojis
        if direction == "above":
            direction_text = "🔼 trên"
            color = 0x00ff88  # Green
        else:
            direction_text = "🔽 dưới"
            color = 0xff4545  # Red
        
        # Create embed with GIÁ: prefix
        embed = discord.Embed(
            title=f"GIÁ: 💰 {token} đã đạt ngưỡng giá mục tiêu!",
            description=f"Giá {token} hiện đã {direction_text} mức giá đặt cảnh báo.",
            color=color,
            timestamp=datetime.now(timezone.utc)
        )
        
        # Add fields
        embed.add_field(name="🪙 Symbol", value=symbol, inline=True)
        embed.add_field(name="🎯 Giá mục tiêu", value=format_price_display(target_price), inline=True)
        embed.add_field(name="💵 Giá hiện tại", value=format_price_display(current_price), inline=True)
        
        # Add change details
        sign = "+" if pct_change > 0 else ""
        embed.add_field(
            name="📊 Biến động gần đây",
            value=f"{sign}{pct_change:.2f}%",
            inline=True
        )
        
        # Add reason
        embed.add_field(
            name="📝 Lý do",
            value=f"Giá đã đạt đến ngưỡng cảnh báo",
            inline=False
        )
        
        # Set footer
        embed.set_footer(text="ChartFix Price Alert System")
        
        return embed
    
    async def _create_daily_change_embed(self, alert_data: Dict[str, Any]) -> discord.Embed:
        """Create embed for daily change alerts"""
        symbol = alert_data.get('symbol', 'UNKNOWN')
        token = symbol.replace('USDT', '')
        current_price = alert_data.get('current_price', 0.0)
        daily_change = alert_data.get('daily_change', 0.0)
        threshold = alert_data.get('threshold', 5.0)

        # Set color and emoji based on change direction
        if daily_change > 0:
            color = 0x00ff88  # Green
            emoji = "🟢"
            direction_text = "TĂNG MẠNH"
            change_str = f"+{daily_change:.2f}%"
        else:
            color = 0xff4545  # Red
            emoji = "🔴"
            direction_text = "GIẢM MẠNH"
            change_str = f"{daily_change:.2f}%"

        # Create simple format as requested with GIÁ: prefix
        title = f"GIÁ: {emoji} {token} {direction_text}"
        description = (
            f"💵 Giá: {format_price_display(current_price)}\n"
            f"📊 24h: {change_str} (>{threshold}%)"
        )

        embed = discord.Embed(
            title=title,
            description=description,
            color=color,
            timestamp=datetime.now(timezone.utc)
        )

        # Set footer
        embed.set_footer(text="ChartFix Alert")

        return embed

    async def _create_short_term_change_embed(self, alert_data: Dict[str, Any]) -> discord.Embed:
        """Create embed for short-term price change alerts (1H, 4H)"""
        symbol = alert_data.get('symbol', 'UNKNOWN')
        token = symbol.replace('USDT', '')
        current_price = alert_data.get('current_price', 0.0)
        percentage_change = alert_data.get('percentage_change', 0.0)
        threshold = alert_data.get('threshold', 0.0)
        timeframe = alert_data.get('timeframe', '1h').upper()
        direction = alert_data.get('direction', 'up')

        # Determine emoji and direction text
        if direction == 'up':
            emoji = "🟢"
            direction_text = "TĂNG MẠNH"
            color = discord.Color.green()
            change_str = f"+{abs(percentage_change):.2f}%"
        else:
            emoji = "🔴"
            direction_text = "GIẢM MẠNH"
            color = discord.Color.red()
            change_str = f"-{abs(percentage_change):.2f}%"

        # Create simple format as requested with GIÁ: prefix
        title = f"GIÁ: {emoji} {token} {direction_text}"
        description = (
            f"💵 Giá: {format_price_display(current_price)}\n"
            f"📊 {timeframe}: {change_str} (>{threshold}%)"
        )

        embed = discord.Embed(
            title=title,
            description=description,
            color=color,
            timestamp=alert_data.get('timestamp', datetime.now(timezone.utc))
        )

        # Add footer with timeframe info
        embed.set_footer(text=f"Short-term price movement alert • {timeframe} timeframe")

        return embed

    async def _send_to_alerts_channel(self, embed: discord.Embed):
        """Send alert to the configured alerts channel"""
        if self.guild_id:
            try:
                guild = self.bot.get_guild(int(self.guild_id))
                if guild:
                    # Try to find existing alerts channel
                    target_channel = None
                    for channel in guild.channels:
                        if hasattr(channel, 'name') and isinstance(channel, discord.TextChannel):
                            if self.channel_name.lower() in channel.name.lower():
                                target_channel = channel
                                break
                    
                    # If not found, try to create it
                    if not target_channel:
                        if guild.me.guild_permissions.manage_channels:
                            target_channel = await guild.create_text_channel(
                                name=self.channel_name,
                                topic="🚨 Automated price alerts for cryptocurrency",
                                reason="Created by ChartFix for price alerts"
                            )
                            
                            # Send welcome message
                            welcome_embed = discord.Embed(
                                title="🚨 Kênh Cảnh Báo Giá",
                                description="Kênh này sẽ nhận các cảnh báo tự động về biến động giá tiền điện tử.",
                                color=0xffa500,
                                timestamp=datetime.now(timezone.utc)
                            )
                            welcome_embed.set_footer(text="ChartFix Price Alert System")
                            await target_channel.send(embed=welcome_embed)
                            
                            logger.info(f"Created new alerts channel: #{target_channel.name}")
                        else:
                            # Try to find general channel as fallback
                            for channel in guild.channels:
                                if hasattr(channel, 'name') and isinstance(channel, discord.TextChannel):
                                    if 'general' in channel.name.lower():
                                        target_channel = channel
                                        break
                    
                    # Send alert if we have a channel
                    if target_channel:
                        await target_channel.send(embed=embed)
                        logger.info(f"Sent price alert to #{target_channel.name}")
                    else:
                        logger.warning("No suitable channel found for price alert")
                else:
                    logger.warning(f"Guild not found: {self.guild_id}")
            except Exception as e:
                logger.error(f"Failed to send price alert to guild: {e}")
        else:
            logger.warning("No guild_id configured for price alerts")

# Global handler instance
_price_alert_handler = None

def get_price_alert_handler(bot) -> PriceAlertHandler:
    """Get or create price alert handler instance"""
    global _price_alert_handler
    if _price_alert_handler is None:
        _price_alert_handler = PriceAlertHandler(bot)
    return _price_alert_handler 