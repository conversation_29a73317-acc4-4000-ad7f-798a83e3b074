import logging
import time
import discord
from discord.ext import commands
from discord import app_commands
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class AdminCommands(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        from utils.config import get_admin_id
        self.admin_id = get_admin_id()

    async def cog_check(self, ctx):
        return self.bot.is_admin(ctx.author.id)

    @app_commands.command(name="stats", description="Display bot statistics and performance metrics")
    async def stats(self, interaction: discord.Interaction):
        if not self.bot.is_admin(interaction.user.id):
            await interaction.response.send_message("❌ This command is restricted to administrators.", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        try:
            stats = self.bot.stats
            uptime = stats.get_uptime()

            # Create stats embed
            embed = discord.Embed(
                title="🤖 Bot Statistics",
                color=0x00ff88,
                timestamp=discord.utils.utcnow()
            )

            # Basic info
            embed.add_field(
                name="📊 Basic Info",
                value=f"**Uptime**: {self._format_timedelta(uptime)}\n"
                      f"**Guilds**: {len(self.bot.guilds)}\n"
                      f"**Users**: {sum(guild.member_count for guild in self.bot.guilds)}\n"
                      f"**Active Watchlists**: {len(self.bot.active_watchlists)}",
                inline=True
            )

            # Command statistics
            total_commands = sum(stats.command_count.values())
            top_commands = sorted(stats.command_count.items(), key=lambda x: x[1], reverse=True)[:5]

            command_text = f"**Total**: {total_commands}\n"
            for cmd, count in top_commands:
                command_text += f"**{cmd}**: {count}\n"

            embed.add_field(
                name="⚡ Commands",
                value=command_text,
                inline=True
            )

            # Error statistics
            total_errors = sum(stats.error_count.values())
            error_text = f"**Total**: {total_errors}\n"

            if stats.error_count:
                top_errors = sorted(stats.error_count.items(), key=lambda x: x[1], reverse=True)[:3]
                for error, count in top_errors:
                    error_text += f"**{error}**: {count}\n"
            else:
                error_text += "No errors recorded"

            embed.add_field(
                name="🚨 Errors",
                value=error_text,
                inline=True
            )

            # Performance metrics
            if stats.response_times:
                avg_times = {cmd: stats.get_avg_response_time(cmd) for cmd in stats.response_times.keys()}
                slowest_cmd = max(avg_times.items(), key=lambda x: x[1])
                fastest_cmd = min(avg_times.items(), key=lambda x: x[1])

                perf_text = f"**Slowest**: {slowest_cmd[0]} ({slowest_cmd[1]:.2f}s)\n"
                perf_text += f"**Fastest**: {fastest_cmd[0]} ({fastest_cmd[1]:.2f}s)"
            else:
                perf_text = "No performance data available"

            embed.add_field(
                name="⚡ Performance",
                value=perf_text,
                inline=True
            )

            # API calls
            total_api_calls = sum(stats.api_calls.values())
            api_text = f"**Total**: {total_api_calls}\n"

            if stats.api_calls:
                top_apis = sorted(stats.api_calls.items(), key=lambda x: x[1], reverse=True)[:3]
                for api, count in top_apis:
                    api_text += f"**{api}**: {count}\n"
            else:
                api_text += "No API calls recorded"

            embed.add_field(
                name="🌐 API Calls",
                value=api_text,
                inline=True
            )

            # System info
            import psutil
            memory_usage = psutil.Process().memory_info().rss / 1024 / 1024  # MB
            cpu_percent = psutil.Process().cpu_percent()

            system_text = f"**Memory**: {memory_usage:.1f} MB\n"
            system_text += f"**CPU**: {cpu_percent:.1f}%\n"
            system_text += f"**Consecutive Errors**: {self.bot.consecutive_errors}"

            embed.add_field(
                name="💻 System",
                value=system_text,
                inline=True
            )

            embed.set_footer(text="Bot Statistics • Admin Only")

            await interaction.followup.send(embed=embed, ephemeral=True)

        except Exception as e:
            logger.error(f"Error in stats command: {e}")
            await interaction.followup.send("❌ Error retrieving statistics.", ephemeral=True)

    @app_commands.command(name="health", description="Perform health check and display system status")
    async def health(self, interaction: discord.Interaction):
        if not self.bot.is_admin(interaction.user.id):
            await interaction.response.send_message("❌ This command is restricted to administrators.", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        try:
            health_status = await self._perform_health_checks()

            embed = discord.Embed(
                title="🏥 Bot Health Check",
                color=0x00ff00 if health_status['overall'] else 0xff0000,
                timestamp=discord.utils.utcnow()
            )

            for check_name, result in health_status['checks'].items():
                status_emoji = "✅" if result['status'] else "❌"
                embed.add_field(
                    name=f"{status_emoji} {check_name}",
                    value=result['message'],
                    inline=True
                )

            embed.set_footer(text="Health Check • Admin Only")

            await interaction.followup.send(embed=embed, ephemeral=True)

        except Exception as e:
            logger.error(f"Error in health command: {e}")
            await interaction.followup.send("❌ Error performing health check.", ephemeral=True)

    @app_commands.command(name="reload", description="Reload bot extensions")
    async def reload(self, interaction: discord.Interaction):
        """Reload bot extensions"""

        if not self.bot.is_admin(interaction.user.id):
            await interaction.response.send_message("❌ This command is restricted to administrators.", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        try:
            extensions = [
                'handlers.discord.market.watchlist_commands',
                'handlers.discord.market.portfolio_commands',
                'handlers.discord.admin.admin_commands'
            ]

            results = []
            for ext in extensions:
                try:
                    await self.bot.reload_extension(ext)
                    results.append(f"✅ {ext}")
                except Exception as e:
                    results.append(f"❌ {ext}: {str(e)}")

            result_text = "\n".join(results)

            embed = discord.Embed(
                title="🔄 Extension Reload",
                description=result_text,
                color=0x00ff88,
                timestamp=discord.utils.utcnow()
            )

            await interaction.followup.send(embed=embed, ephemeral=True)

        except Exception as e:
            logger.error(f"Error in reload command: {e}")
            await interaction.followup.send("❌ Error reloading extensions.", ephemeral=True)

    def _format_timedelta(self, td: timedelta) -> str:
        """Format timedelta to human readable string"""
        days = td.days
        hours, remainder = divmod(td.seconds, 3600)
        minutes, seconds = divmod(remainder, 60)

        parts = []
        if days:
            parts.append(f"{days}d")
        if hours:
            parts.append(f"{hours}h")
        if minutes:
            parts.append(f"{minutes}m")
        if seconds or not parts:
            parts.append(f"{seconds}s")

        return " ".join(parts)

    async def _perform_health_checks(self) -> dict:
        """Perform comprehensive health checks"""
        checks = {}

        # Database connectivity
        try:
            # Test database connection by executing a simple query
            self.bot.db_service.execute_query("SELECT 1")
            checks['Database'] = {'status': True, 'message': 'Connected and responsive'}
        except Exception as e:
            checks['Database'] = {'status': False, 'message': f'Error: {str(e)}'}

        # Binance API connectivity
        try:
            # Test API call using market service
            from services.market.market_service import get_market_service
            market_service = get_market_service()
            test_prices = await market_service.get_prices(['BTCUSDT'])
            if test_prices and 'BTCUSDT' in test_prices:
                checks['Binance API'] = {'status': True, 'message': 'API responsive'}
            else:
                checks['Binance API'] = {'status': False, 'message': 'No price data received'}
        except Exception as e:
            checks['Binance API'] = {'status': False, 'message': f'Error: {str(e)}'}

        # Discord connectivity
        checks['Discord'] = {'status': True, 'message': f'Connected to {len(self.bot.guilds)} guilds'}

        # Background tasks
        watchlist_running = self.bot.watchlist_updater.is_running()
        health_running = self.bot.health_monitor.is_running()

        if watchlist_running and health_running:
            checks['Background Tasks'] = {'status': True, 'message': 'All tasks running'}
        else:
            checks['Background Tasks'] = {'status': False, 'message': 'Some tasks not running'}

        # Overall health
        overall_health = all(check['status'] for check in checks.values())

        return {
            'overall': overall_health,
            'checks': checks
        }



    @app_commands.command(name="setup_market_news", description="[ADMIN] Setup market news channel and check permissions")
    async def setup_market_news(self, interaction: discord.Interaction):
        """Setup market news channel and check permissions"""
        start_time = time.time()

        # Check if user is admin
        if not self.bot.is_admin(interaction.user.id):
            await interaction.response.send_message("❌ You don't have permission to use this command.", ephemeral=True)
            return

        try:
            await interaction.response.defer(ephemeral=True)

            logger.info(f"Admin {interaction.user} setting up market news channel")

            guild = interaction.guild
            if not guild:
                await interaction.followup.send("❌ This command must be used in a server.", ephemeral=True)
                return

            # Check bot permissions
            bot_member = guild.me
            permissions = bot_member.guild_permissions

            embed = discord.Embed(
                title="📰 Market News Channel Setup",
                color=0x00ff88,
                timestamp=discord.utils.utcnow()
            )

            # Permission check
            permission_status = []
            required_perms = {
                "Send Messages": permissions.send_messages,
                "Embed Links": permissions.embed_links,
                "Read Message History": permissions.read_message_history,
                "Manage Messages": permissions.manage_messages,
                "Manage Channels": permissions.manage_channels
            }

            for perm_name, has_perm in required_perms.items():
                status = "✅" if has_perm else "❌"
                permission_status.append(f"{status} {perm_name}")

            embed.add_field(
                name="🔐 Bot Permissions",
                value="\n".join(permission_status),
                inline=False
            )

            # Check for existing market-news channel
            market_channel = None
            for channel in guild.text_channels:
                if channel.name == "market-news":
                    market_channel = channel
                    break

            if market_channel:
                embed.add_field(
                    name="📺 Channel Status",
                    value=f"✅ Found existing channel: {market_channel.mention}",
                    inline=False
                )
            else:
                if permissions.manage_channels:
                    try:
                        # Create the channel
                        market_channel = await guild.create_text_channel(
                            name="market-news",
                            topic="📰 Automated daily market news and reports",
                            reason="Created by ChartFix bot admin command"
                        )
                        embed.add_field(
                            name="📺 Channel Status",
                            value=f"✅ Created new channel: {market_channel.mention}",
                            inline=False
                        )

                        # Send welcome message to the new channel
                        welcome_embed = discord.Embed(
                            title="📰 Welcome to Market News!",
                            description="This channel will receive automated daily market reports at 00:00 UTC.\n\n"
                                      "**What you'll get:**\n"
                                      "📊 Crypto market overview\n"
                                      "🌍 Global market indices\n"
                                      "🔥 Trending cryptocurrencies\n"
                                      "📰 Latest crypto & financial news\n"
                                      "🧠 Market sentiment analysis",
                            color=0x00ff88,
                            timestamp=discord.utils.utcnow()
                        )
                        welcome_embed.set_footer(text="ChartFix Market News System")
                        await market_channel.send(embed=welcome_embed)

                    except Exception as e:
                        embed.add_field(
                            name="📺 Channel Status",
                            value=f"❌ Failed to create channel: {str(e)}",
                            inline=False
                        )
                else:
                    embed.add_field(
                        name="📺 Channel Status",
                        value="❌ No market-news channel found and bot lacks Manage Channels permission",
                        inline=False
                    )
                    embed.add_field(
                        name="🛠️ Manual Setup",
                        value="Please create a channel named `market-news` manually or grant the bot `Manage Channels` permission.",
                        inline=False
                    )

            # Add setup instructions
            if not permissions.manage_channels:
                embed.add_field(
                    name="🔧 Fix Permissions",
                    value="To grant Manage Channels permission:\n"
                          "1. Go to Server Settings → Roles\n"
                          "2. Find the bot's role\n"
                          "3. Enable 'Manage Channels' permission\n"
                          "4. Or re-invite the bot with updated permissions",
                    inline=False
                )

            embed.set_footer(text=f"ChartFix Admin Tools • Setup completed in {time.time() - start_time:.2f}s")

            await interaction.followup.send(embed=embed, ephemeral=True)

            # Record command execution
            await self.bot.record_command_execution("setup_market_news", start_time, True)

        except Exception as e:
            logger.error(f"Error in setup_market_news command: {e}")

            error_embed = discord.Embed(
                title="❌ Command Error",
                description=f"Error setting up market news: {str(e)}",
                color=0xff4444,
                timestamp=discord.utils.utcnow()
            )

            try:
                await interaction.followup.send(embed=error_embed, ephemeral=True)
            except:
                pass

            await self.bot.record_command_execution("setup_market_news", start_time, False)

    @app_commands.command(name="pin", description="Pin a message by ID")
    @app_commands.describe(message_id="ID of the message to pin")
    async def pin_message(self, interaction: discord.Interaction, message_id: str):
        """Pin a message by its ID"""
        if not self.bot.is_admin(interaction.user.id):
            await interaction.response.send_message("❌ This command is restricted to administrators.", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        try:
            # Convert message_id to int
            msg_id = int(message_id)

            # Get the message from current channel
            channel = interaction.channel
            message = await channel.fetch_message(msg_id)

            # Check if bot has pin permissions
            if not channel.permissions_for(interaction.guild.me).manage_messages:
                await interaction.followup.send("❌ Bot doesn't have permission to pin messages in this channel.", ephemeral=True)
                return

            # Pin the message
            await message.pin()

            embed = discord.Embed(
                title="📌 Message Pinned",
                description=f"Successfully pinned message ID: `{message_id}`",
                color=0x00ff00,
                timestamp=discord.utils.utcnow()
            )

            # Add message preview and info
            author_name = message.author.display_name if message.author else 'Unknown'
            message_info = f"**Author:** {author_name}\n**Created:** <t:{int(message.created_at.timestamp())}:R>"

            if message.content:
                preview = message.content[:100] + "..." if len(message.content) > 100 else message.content
                embed.add_field(name="Message Preview", value=f"```{preview}```", inline=False)
                embed.add_field(name="Message Info", value=message_info, inline=False)
            else:
                embed.add_field(name="Message Info", value=f"{message_info}\n**Type:** Embed/Attachment", inline=False)

            embed.set_footer(text="Pin Command • Admin Only")
            await interaction.followup.send(embed=embed, ephemeral=True)

            logger.info(f"Admin {interaction.user} pinned message {message_id} in {channel.name}")

        except ValueError:
            await interaction.followup.send("❌ Invalid message ID format. Please provide a valid number.", ephemeral=True)
        except discord.NotFound:
            await interaction.followup.send("❌ Message not found. Make sure the message ID is correct and in this channel.", ephemeral=True)
        except discord.HTTPException as e:
            if "already pinned" in str(e).lower():
                await interaction.followup.send("⚠️ This message is already pinned.", ephemeral=True)
            else:
                await interaction.followup.send(f"❌ Failed to pin message: {str(e)}", ephemeral=True)
        except Exception as e:
            logger.error(f"Error in pin command: {e}")
            await interaction.followup.send("❌ An error occurred while pinning the message.", ephemeral=True)

    @app_commands.command(name="unpin", description="Unpin a message by ID")
    @app_commands.describe(message_id="ID of the message to unpin")
    async def unpin_message(self, interaction: discord.Interaction, message_id: str):
        """Unpin a message by its ID"""
        if not self.bot.is_admin(interaction.user.id):
            await interaction.response.send_message("❌ This command is restricted to administrators.", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        try:
            # Convert message_id to int
            msg_id = int(message_id)

            # Get the message from current channel
            channel = interaction.channel
            message = await channel.fetch_message(msg_id)

            # Check if bot has pin permissions
            if not channel.permissions_for(interaction.guild.me).manage_messages:
                await interaction.followup.send("❌ Bot doesn't have permission to manage pins in this channel.", ephemeral=True)
                return

            # Unpin the message
            await message.unpin()

            embed = discord.Embed(
                title="📌 Message Unpinned",
                description=f"Successfully unpinned message ID: `{message_id}`",
                color=0xff9900,
                timestamp=discord.utils.utcnow()
            )

            embed.set_footer(text="Unpin Command • Admin Only")
            await interaction.followup.send(embed=embed, ephemeral=True)

            logger.info(f"Admin {interaction.user} unpinned message {message_id} in {channel.name}")

        except ValueError:
            await interaction.followup.send("❌ Invalid message ID format. Please provide a valid number.", ephemeral=True)
        except discord.NotFound:
            await interaction.followup.send("❌ Message not found. Make sure the message ID is correct and in this channel.", ephemeral=True)
        except discord.HTTPException as e:
            if "not pinned" in str(e).lower():
                await interaction.followup.send("⚠️ This message is not pinned.", ephemeral=True)
            else:
                await interaction.followup.send(f"❌ Failed to unpin message: {str(e)}", ephemeral=True)
        except Exception as e:
            logger.error(f"Error in unpin command: {e}")
            await interaction.followup.send("❌ An error occurred while unpinning the message.", ephemeral=True)

    @app_commands.command(name="pins", description="List all pinned messages in current channel")
    async def list_pins(self, interaction: discord.Interaction):
        """List all pinned messages in the current channel"""
        if not self.bot.is_admin(interaction.user.id):
            await interaction.response.send_message("❌ This command is restricted to administrators.", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        try:
            channel = interaction.channel
            pinned_messages = await channel.pins()

            if not pinned_messages:
                await interaction.followup.send("📌 No pinned messages in this channel.", ephemeral=True)
                return

            embed = discord.Embed(
                title=f"📌 Pinned Messages in #{channel.name}",
                description=f"Found {len(pinned_messages)} pinned message(s)",
                color=0x0099ff,
                timestamp=discord.utils.utcnow()
            )

            for i, message in enumerate(pinned_messages[:10], 1):  # Limit to 10 messages
                author_name = message.author.display_name if message.author else "Unknown"

                # Content preview
                if message.content:
                    content_preview = message.content[:50] + "..." if len(message.content) > 50 else message.content
                    message_type = "Text"
                else:
                    content_preview = "*[Embed or attachment]*"
                    message_type = "Embed/Attachment"

                embed.add_field(
                    name=f"{i}. Message ID: {message.id}",
                    value=f"**Author:** {author_name}\n**Content:** {content_preview}\n**Created:** <t:{int(message.created_at.timestamp())}:R>",
                    inline=False
                )

            if len(pinned_messages) > 10:
                embed.add_field(
                    name="Note",
                    value=f"Showing first 10 of {len(pinned_messages)} pinned messages",
                    inline=False
                )

            embed.set_footer(text="Pins List • Admin Only")
            await interaction.followup.send(embed=embed, ephemeral=True)

        except Exception as e:
            logger.error(f"Error in pins command: {e}")
            await interaction.followup.send("❌ An error occurred while fetching pinned messages.", ephemeral=True)

    @app_commands.command(name="clear", description="Xóa tất cả tin nhắn trong kênh hiện tại")
    @app_commands.describe(
        confirm="Nhập 'ok' để xác nhận xóa tất cả tin nhắn",
        keep_pinned="Giữ lại tin nhắn đã ghim (mặc định: Có)"
    )
    async def clear_channel(self, interaction: discord.Interaction, confirm: str, keep_pinned: bool = True):
        """Clear all messages in the current channel with safety confirmation"""
        if not self.bot.is_admin(interaction.user.id):
            await interaction.response.send_message("❌ This command is restricted to administrators.", ephemeral=True)
            return

        # Safety confirmation check
        if confirm.lower() != "ok":
            embed = discord.Embed(
                title="⚠️ Xác nhận xóa kênh",
                description="**Hành động này sẽ xóa TẤT CẢ tin nhắn trong kênh này!**\n\n"
                           "Để tiếp tục, hãy sử dụng lệnh sau:\n"
                           "`/clear confirm:ok`\n\n"
                           "**Tùy chọn:**\n"
                           "• `keep_pinned:True` - Giữ lại tin nhắn đã ghim (mặc định)\n"
                           "• `keep_pinned:False` - Xóa tất cả kể cả tin nhắn đã ghim",
                color=0xff4444,
                timestamp=discord.utils.utcnow()
            )
            embed.set_footer(text="Lệnh Xóa • Chỉ dành cho Admin • Hành động nguy hiểm")
            await interaction.response.send_message(embed=embed, ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        try:
            channel = interaction.channel

            # Check bot permissions
            if not channel.permissions_for(interaction.guild.me).manage_messages:
                await interaction.followup.send("❌ Bot doesn't have permission to manage messages in this channel.", ephemeral=True)
                return

            # Get pinned messages if we need to preserve them
            pinned_messages = []
            if keep_pinned:
                pinned_messages = await channel.pins()

            # Count total messages before deletion
            message_count = 0
            async for _ in channel.history(limit=None):
                message_count += 1

            if message_count == 0:
                await interaction.followup.send("📭 Channel is already empty.", ephemeral=True)
                return

            # Send progress notification
            progress_embed = discord.Embed(
                title="🧹 Clearing Channel...",
                description=f"Deleting {message_count} messages from #{channel.name}\n"
                           f"Pinned messages: {'Preserved' if keep_pinned else 'Will be deleted'}",
                color=0xffa500,
                timestamp=discord.utils.utcnow()
            )
            await interaction.followup.send(embed=progress_embed, ephemeral=True)

            # Clear messages in batches
            deleted_count = 0

            # Method 1: Try bulk delete for recent messages (< 14 days)
            try:
                messages_to_delete = []
                async for message in channel.history(limit=None):
                    if keep_pinned and message.pinned:
                        continue
                    messages_to_delete.append(message)

                    # Bulk delete in batches of 100
                    if len(messages_to_delete) >= 100:
                        await channel.delete_messages(messages_to_delete)
                        deleted_count += len(messages_to_delete)
                        messages_to_delete = []

                # Delete remaining messages
                if messages_to_delete:
                    if len(messages_to_delete) == 1:
                        await messages_to_delete[0].delete()
                    else:
                        await channel.delete_messages(messages_to_delete)
                    deleted_count += len(messages_to_delete)

            except discord.HTTPException:
                # Fallback: Delete messages one by one (for older messages)
                async for message in channel.history(limit=None):
                    if keep_pinned and message.pinned:
                        continue
                    try:
                        await message.delete()
                        deleted_count += 1
                    except discord.NotFound:
                        pass  # Message already deleted
                    except discord.HTTPException:
                        pass  # Skip messages that can't be deleted

            # Send completion notification
            completion_embed = discord.Embed(
                title="✅ Channel Cleared Successfully",
                description=f"**Deleted:** {deleted_count} messages\n"
                           f"**Channel:** #{channel.name}\n"
                           f"**Pinned messages:** {'Preserved' if keep_pinned else 'Deleted'}\n"
                           f"**Executed by:** {interaction.user.mention}",
                color=0x00ff00,
                timestamp=discord.utils.utcnow()
            )
            completion_embed.set_footer(text="Clear Command • Admin Only")

            # Send to admin privately
            await interaction.followup.send(embed=completion_embed, ephemeral=True)

            # Log the action
            logger.warning(f"Admin {interaction.user} cleared {deleted_count} messages from #{channel.name} (keep_pinned: {keep_pinned})")

        except discord.Forbidden:
            await interaction.followup.send("❌ Bot doesn't have permission to delete messages in this channel.", ephemeral=True)
        except Exception as e:
            logger.error(f"Error in clear command: {e}")
            await interaction.followup.send(f"❌ An error occurred while clearing the channel: {str(e)}", ephemeral=True)

async def setup(bot):
    """Setup function called by Discord.py"""
    await bot.add_cog(AdminCommands(bot))
