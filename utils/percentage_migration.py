"""
Migration utility to standardize percentage calculations across the system
This script helps identify and update inconsistent percentage calculation methods
"""

import logging
import re
from typing import List, Dict, Any, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)

class PercentageMigrationTool:
    """Tool to help migrate percentage calculations to unified service"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.patterns_to_find = [
            # Direct percentage calculations
            r'\(\s*\(\s*[\w\.\[\]]+\s*-\s*[\w\.\[\]]+\s*\)\s*/\s*[\w\.\[\]]+\s*\)\s*\*\s*100',
            # Ticker percentage extractions
            r'ticker\.get\([\'"]percentage[\'"]',
            # CoinGecko percentage extractions
            r'price_change_percentage_24h',
            # Manual OHLCV calculations
            r'daily_open.*change_percent',
        ]
        
        self.files_to_check = [
            'handlers/discord/market/*.py',
            'handlers/discord/alerts/*.py',
            'services/market/*.py',
            'utils/ui_components.py',
            'bot.py'
        ]
    
    def scan_for_inconsistencies(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        Scan codebase for percentage calculation inconsistencies
        
        Returns:
            Dictionary mapping file paths to found issues
        """
        issues = {}
        
        for file_pattern in self.files_to_check:
            files = list(self.project_root.glob(file_pattern))
            
            for file_path in files:
                if file_path.is_file() and file_path.suffix == '.py':
                    file_issues = self._scan_file(file_path)
                    if file_issues:
                        issues[str(file_path.relative_to(self.project_root))] = file_issues
        
        return issues
    
    def _scan_file(self, file_path: Path) -> List[Dict[str, Any]]:
        """
        Scan individual file for percentage calculation patterns
        
        Args:
            file_path: Path to file to scan
            
        Returns:
            List of issues found in the file
        """
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            for line_num, line in enumerate(lines, 1):
                for pattern in self.patterns_to_find:
                    matches = re.finditer(pattern, line)
                    for match in matches:
                        issues.append({
                            'line_number': line_num,
                            'line_content': line.strip(),
                            'pattern_matched': pattern,
                            'match_text': match.group(),
                            'issue_type': self._classify_issue(pattern, match.group())
                        })
        
        except Exception as e:
            logger.error(f"Error scanning file {file_path}: {e}")
        
        return issues
    
    def _classify_issue(self, pattern: str, match_text: str) -> str:
        """
        Classify the type of percentage calculation issue
        
        Args:
            pattern: Regex pattern that matched
            match_text: The actual matched text
            
        Returns:
            Classification of the issue type
        """
        if 'percentage' in match_text.lower():
            if 'ticker' in match_text:
                return 'binance_ticker_extraction'
            elif 'price_change_percentage' in match_text:
                return 'coingecko_extraction'
        elif '* 100' in match_text:
            return 'manual_calculation'
        elif 'daily_open' in match_text:
            return 'daily_open_calculation'
        else:
            return 'unknown_calculation'
    
    def generate_migration_report(self) -> str:
        """
        Generate a comprehensive migration report
        
        Returns:
            Formatted report string
        """
        issues = self.scan_for_inconsistencies()
        
        report = []
        report.append("=" * 80)
        report.append("PERCENTAGE CALCULATION MIGRATION REPORT")
        report.append("=" * 80)
        report.append("")
        
        if not issues:
            report.append("✅ No percentage calculation inconsistencies found!")
            return "\n".join(report)
        
        # Summary
        total_issues = sum(len(file_issues) for file_issues in issues.values())
        report.append(f"📊 SUMMARY:")
        report.append(f"   Files with issues: {len(issues)}")
        report.append(f"   Total issues found: {total_issues}")
        report.append("")
        
        # Issue breakdown by type
        issue_types = {}
        for file_issues in issues.values():
            for issue in file_issues:
                issue_type = issue['issue_type']
                issue_types[issue_type] = issue_types.get(issue_type, 0) + 1
        
        report.append("📈 ISSUE BREAKDOWN:")
        for issue_type, count in sorted(issue_types.items()):
            report.append(f"   {issue_type}: {count} occurrences")
        report.append("")
        
        # Detailed file-by-file breakdown
        report.append("📁 DETAILED BREAKDOWN:")
        report.append("")
        
        for file_path, file_issues in issues.items():
            report.append(f"🔍 {file_path}")
            report.append("-" * len(file_path))
            
            for issue in file_issues:
                report.append(f"   Line {issue['line_number']}: {issue['issue_type']}")
                report.append(f"      Code: {issue['line_content']}")
                report.append(f"      Match: {issue['match_text']}")
                report.append("")
        
        # Migration recommendations
        report.append("🎯 MIGRATION RECOMMENDATIONS:")
        report.append("")
        report.append("1. Replace all ticker.get('percentage') calls with:")
        report.append("   percentage_service.extract_binance_percentage(ticker)")
        report.append("")
        report.append("2. Replace all price_change_percentage_24h extractions with:")
        report.append("   percentage_service.extract_coingecko_percentage(coin_data)")
        report.append("")
        report.append("3. Replace manual calculations with:")
        report.append("   percentage_service.calculate_percentage_change(current, base)")
        report.append("")
        report.append("4. Update daily_open calculations to use rolling_24h method")
        report.append("")
        
        return "\n".join(report)
    
    def suggest_fixes(self, file_path: str) -> List[Dict[str, str]]:
        """
        Suggest specific fixes for a file
        
        Args:
            file_path: Path to file to analyze
            
        Returns:
            List of suggested fixes
        """
        fixes = []
        
        # This would contain specific replacement suggestions
        # Implementation would depend on the specific patterns found
        
        return fixes

def run_migration_analysis():
    """Run the migration analysis and print report"""
    tool = PercentageMigrationTool()
    report = tool.generate_migration_report()
    print(report)
    
    # Save report to file
    report_path = Path(__file__).parent.parent / 'logs' / 'percentage_migration_report.txt'
    report_path.parent.mkdir(exist_ok=True)
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n📄 Report saved to: {report_path}")

if __name__ == "__main__":
    run_migration_analysis()
