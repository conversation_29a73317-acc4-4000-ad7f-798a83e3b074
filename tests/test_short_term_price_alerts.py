"""
Test suite for short-term price alerts (1H and 4H)
"""

import unittest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
import sys
import os
from datetime import datetime, timezone

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.market.short_term_price_alert_service import (
    ShortTermPriceAlertService,
    ShortTermAlert,
    get_short_term_alert_service
)

class TestShortTermPriceAlerts(unittest.TestCase):
    """Test short-term price alert functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.service = ShortTermPriceAlertService()
        
        # Mock configuration
        self.service.enabled = True
        self.service.timeframe_configs = {
            '1h': {'enabled': True, 'thresholds': [3, 5, 8]},
            '4h': {'enabled': True, 'thresholds': [4, 7, 10]}
        }
        
        # Sample OHLCV data
        self.sample_ohlcv_1h = [
            {
                'timestamp': 1640995200000,  # Previous hour
                'open': 48000.0,
                'high': 48500.0,
                'low': 47800.0,
                'close': 48200.0,
                'volume': 1000.0
            },
            {
                'timestamp': 1640998800000,  # Current hour
                'open': 48200.0,
                'high': 50000.0,
                'low': 48000.0,
                'close': 50000.0,  # +3.73% change
                'volume': 1200.0
            }
        ]
        
        self.sample_ohlcv_4h = [
            {
                'timestamp': 1640980800000,  # Previous 4h
                'open': 45000.0,
                'high': 46000.0,
                'low': 44500.0,
                'close': 45500.0,
                'volume': 4000.0
            },
            {
                'timestamp': 1640995200000,  # Current 4h
                'open': 45500.0,
                'high': 48000.0,
                'low': 45000.0,
                'close': 47500.0,  # +4.40% change
                'volume': 4500.0
            }
        ]
    
    def test_configuration_loading(self):
        """Test configuration loading"""
        # Test enabled status
        self.assertTrue(self.service.enabled)
        
        # Test timeframe configurations
        self.assertIn('1h', self.service.timeframe_configs)
        self.assertIn('4h', self.service.timeframe_configs)
        
        # Test thresholds
        self.assertEqual(self.service.timeframe_configs['1h']['thresholds'], [3, 5, 8])
        self.assertEqual(self.service.timeframe_configs['4h']['thresholds'], [4, 7, 10])
    
    @patch('services.market.short_term_price_alert_service.load_config')
    def test_watchlist_symbols_retrieval(self, mock_load_config):
        """Test watchlist symbols retrieval"""
        mock_load_config.return_value = {
            'market': {
                'watchlist_symbols': ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
            }
        }
        
        symbols = self.service.get_watchlist_symbols()
        self.assertEqual(symbols, ['BTCUSDT', 'ETHUSDT', 'BNBUSDT'])
    
    async def test_price_change_calculation_1h(self):
        """Test 1H price change calculation"""
        # Mock OHLCV data fetch
        self.service.fetch_ohlcv_data = AsyncMock(return_value=self.sample_ohlcv_1h)
        
        result = await self.service.calculate_price_change('BTCUSDT', '1h')
        
        self.assertIsNotNone(result)
        self.assertEqual(result['symbol'], 'BTCUSDT')
        self.assertEqual(result['timeframe'], '1h')
        self.assertEqual(result['current_price'], 50000.0)
        self.assertEqual(result['previous_price'], 48200.0)
        self.assertAlmostEqual(result['percentage_change'], 3.73, places=1)
    
    async def test_price_change_calculation_4h(self):
        """Test 4H price change calculation"""
        # Mock OHLCV data fetch
        self.service.fetch_ohlcv_data = AsyncMock(return_value=self.sample_ohlcv_4h)
        
        result = await self.service.calculate_price_change('BTCUSDT', '4h')
        
        self.assertIsNotNone(result)
        self.assertEqual(result['symbol'], 'BTCUSDT')
        self.assertEqual(result['timeframe'], '4h')
        self.assertEqual(result['current_price'], 47500.0)
        self.assertEqual(result['previous_price'], 45500.0)
        self.assertAlmostEqual(result['percentage_change'], 4.40, places=1)
    
    def test_alert_cooldown_mechanism(self):
        """Test alert cooldown to prevent spam"""
        # First alert should be allowed
        self.assertTrue(self.service.should_send_alert('BTCUSDT', '1h', 5.0))
        
        # Second alert immediately should be blocked
        self.assertFalse(self.service.should_send_alert('BTCUSDT', '1h', 5.0))
        
        # Different threshold should be allowed
        self.assertTrue(self.service.should_send_alert('BTCUSDT', '1h', 8.0))
        
        # Different symbol should be allowed
        self.assertTrue(self.service.should_send_alert('ETHUSDT', '1h', 5.0))
    
    def test_alert_data_structure(self):
        """Test ShortTermAlert data structure"""
        alert = ShortTermAlert(
            symbol='BTCUSDT',
            timeframe='1h',
            current_price=50000.0,
            percentage_change=5.25,
            threshold=5.0,
            direction='up',
            timestamp=datetime.now(timezone.utc)
        )
        
        self.assertEqual(alert.symbol, 'BTCUSDT')
        self.assertEqual(alert.timeframe, '1h')
        self.assertEqual(alert.current_price, 50000.0)
        self.assertEqual(alert.percentage_change, 5.25)
        self.assertEqual(alert.threshold, 5.0)
        self.assertEqual(alert.direction, 'up')
        self.assertIsInstance(alert.timestamp, datetime)
    
    async def test_alert_triggering_logic(self):
        """Test alert triggering logic"""
        # Mock dependencies
        self.service.get_watchlist_symbols = Mock(return_value=['BTCUSDT'])
        self.service.calculate_price_change = AsyncMock(return_value={
            'symbol': 'BTCUSDT',
            'timeframe': '1h',
            'current_price': 50000.0,
            'previous_price': 48000.0,
            'percentage_change': 4.17,  # Above 3% threshold
            'timestamp': datetime.now(timezone.utc)
        })
        
        # Mock alert sending
        alerts_sent = []
        async def mock_send_alert(alert):
            alerts_sent.append(alert)
        
        self.service._send_alert = mock_send_alert
        
        # Run check
        await self.service.check_price_movements()
        
        # Verify alert was triggered
        self.assertEqual(len(alerts_sent), 1)
        alert = alerts_sent[0]
        self.assertEqual(alert.symbol, 'BTCUSDT')
        self.assertEqual(alert.timeframe, '1h')
        self.assertEqual(alert.threshold, 3)  # First threshold that was exceeded
        self.assertEqual(alert.direction, 'up')
    
    async def test_negative_price_change_alert(self):
        """Test alert for negative price changes"""
        # Mock negative price change
        self.service.get_watchlist_symbols = Mock(return_value=['ETHUSDT'])
        self.service.calculate_price_change = AsyncMock(return_value={
            'symbol': 'ETHUSDT',
            'timeframe': '4h',
            'current_price': 3000.0,
            'previous_price': 3200.0,
            'percentage_change': -6.25,  # Above 4% threshold (absolute)
            'timestamp': datetime.now(timezone.utc)
        })
        
        alerts_sent = []
        async def mock_send_alert(alert):
            alerts_sent.append(alert)
        
        self.service._send_alert = mock_send_alert
        
        await self.service.check_price_movements()
        
        # Verify negative alert
        self.assertEqual(len(alerts_sent), 1)
        alert = alerts_sent[0]
        self.assertEqual(alert.direction, 'down')
        self.assertEqual(alert.percentage_change, -6.25)
    
    def test_alert_callback_registration(self):
        """Test alert callback registration"""
        callback_called = []
        
        async def test_callback(alert_type, alert_data):
            callback_called.append((alert_type, alert_data))
        
        self.service.add_alert_callback(test_callback)
        self.assertIn(test_callback, self.service.alert_callbacks)
    
    async def test_insufficient_data_handling(self):
        """Test handling of insufficient OHLCV data"""
        # Mock insufficient data
        self.service.fetch_ohlcv_data = AsyncMock(return_value=[])
        
        result = await self.service.calculate_price_change('BTCUSDT', '1h')
        self.assertIsNone(result)
    
    def test_global_service_instance(self):
        """Test global service instance"""
        service1 = get_short_term_alert_service()
        service2 = get_short_term_alert_service()
        
        # Should return the same instance
        self.assertIs(service1, service2)

class TestShortTermAlertIntegration(unittest.TestCase):
    """Test integration with existing systems"""
    
    @patch('services.market.short_term_price_alert_service.get_binance_futures_exchange')
    def test_exchange_integration(self, mock_get_exchange):
        """Test Binance exchange integration"""
        mock_exchange = Mock()
        mock_get_exchange.return_value = mock_exchange
        
        service = ShortTermPriceAlertService()
        service._init_exchange()
        
        self.assertEqual(service.exchange, mock_exchange)
    
    def test_percentage_service_integration(self):
        """Test integration with percentage calculation service"""
        service = ShortTermPriceAlertService()
        
        # Should have percentage service initialized
        self.assertIsNotNone(service.percentage_service)
        
        # Should use unified calculation method
        from services.market.percentage_calculation_service import CalculationMethod
        self.assertEqual(service.percentage_service.default_method, CalculationMethod.ROLLING_24H)

def run_short_term_alert_tests():
    """Run all short-term price alert tests"""
    print("🧪 Running Short-term Price Alert Tests...")
    print("=" * 60)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestShortTermPriceAlerts))
    suite.addTests(loader.loadTestsFromTestCase(TestShortTermAlertIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✅ All short-term price alert tests passed!")
        print(f"📊 Tests run: {result.testsRun}")
    else:
        print("❌ Some tests failed!")
        print(f"📊 Tests run: {result.testsRun}")
        print(f"❌ Failures: {len(result.failures)}")
        print(f"💥 Errors: {len(result.errors)}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    # Run tests in async context for async test methods
    async def run_async_tests():
        return run_short_term_alert_tests()
    
    asyncio.run(run_async_tests())
