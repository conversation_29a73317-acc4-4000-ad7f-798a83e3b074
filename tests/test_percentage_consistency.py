"""
Test suite to ensure percentage calculation consistency across the system
"""

import unittest
import asyncio
from unittest.mock import Mock, patch
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.market.percentage_calculation_service import (
    get_percentage_service, 
    PercentageCalculationService,
    CalculationMethod,
    DataSource
)

class TestPercentageConsistency(unittest.TestCase):
    """Test percentage calculation consistency"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.service = get_percentage_service()
        
        # Sample ticker data from Binance
        self.sample_binance_ticker = {
            'symbol': 'BTC/USDT:USDT',
            'last': 50000.0,
            'close': 50000.0,
            'open': 48000.0,
            'percentage': 4.17,  # (50000 - 48000) / 48000 * 100
            'high': 51000.0,
            'low': 47500.0,
            'quoteVolume': 1000000000.0
        }
        
        # Sample CoinGecko data
        self.sample_coingecko_coin = {
            'id': 'bitcoin',
            'symbol': 'btc',
            'name': 'Bitcoin',
            'current_price': 50000.0,
            'price_change_percentage_24h': 4.17,
            'total_volume': 1000000000.0
        }
    
    def test_binance_percentage_extraction(self):
        """Test Binance percentage extraction consistency"""
        result = self.service.extract_binance_percentage(self.sample_binance_ticker)
        
        self.assertTrue(result.is_valid)
        self.assertEqual(result.value, 4.17)
        self.assertEqual(result.method, CalculationMethod.ROLLING_24H)
        self.assertEqual(result.source, DataSource.BINANCE_FUTURES)
        self.assertEqual(result.current_price, 50000.0)
    
    def test_coingecko_percentage_extraction(self):
        """Test CoinGecko percentage extraction consistency"""
        result = self.service.extract_coingecko_percentage(self.sample_coingecko_coin)
        
        self.assertTrue(result.is_valid)
        self.assertEqual(result.value, 4.17)
        self.assertEqual(result.method, CalculationMethod.ROLLING_24H)
        self.assertEqual(result.source, DataSource.COINGECKO)
        self.assertEqual(result.current_price, 50000.0)
    
    def test_manual_calculation_consistency(self):
        """Test manual percentage calculation"""
        result = self.service.calculate_percentage_change(
            current_price=50000.0,
            base_price=48000.0,
            method=CalculationMethod.ROLLING_24H
        )
        
        self.assertTrue(result.is_valid)
        self.assertAlmostEqual(result.value, 4.17, places=2)
        self.assertEqual(result.method, CalculationMethod.ROLLING_24H)
        self.assertEqual(result.current_price, 50000.0)
        self.assertEqual(result.base_price, 48000.0)
    
    def test_percentage_display_formatting(self):
        """Test consistent percentage display formatting"""
        # Positive percentage
        positive_result = self.service.calculate_percentage_change(50000.0, 48000.0)
        formatted_positive = self.service.format_percentage_display(positive_result)
        self.assertIn("🟢", formatted_positive)
        self.assertIn("+4.17%", formatted_positive)
        
        # Negative percentage
        negative_result = self.service.calculate_percentage_change(48000.0, 50000.0)
        formatted_negative = self.service.format_percentage_display(negative_result)
        self.assertIn("🔴", formatted_negative)
        self.assertIn("-4.00%", formatted_negative)
        
        # Zero percentage
        zero_result = self.service.calculate_percentage_change(50000.0, 50000.0)
        formatted_zero = self.service.format_percentage_display(zero_result)
        self.assertIn("⚪", formatted_zero)
        self.assertIn("0.00%", formatted_zero)
    
    def test_data_source_consistency(self):
        """Test that same percentage values come from different sources"""
        binance_result = self.service.extract_binance_percentage(self.sample_binance_ticker)
        coingecko_result = self.service.extract_coingecko_percentage(self.sample_coingecko_coin)
        manual_result = self.service.calculate_percentage_change(50000.0, 48000.0)
        
        # All should give the same percentage value
        self.assertAlmostEqual(binance_result.value, coingecko_result.value, places=2)
        self.assertAlmostEqual(binance_result.value, manual_result.value, places=2)
        
        # All should use the same calculation method
        self.assertEqual(binance_result.method, CalculationMethod.ROLLING_24H)
        self.assertEqual(coingecko_result.method, CalculationMethod.ROLLING_24H)
        self.assertEqual(manual_result.method, CalculationMethod.ROLLING_24H)
    
    def test_error_handling(self):
        """Test error handling for invalid data"""
        # Invalid ticker data
        invalid_ticker = {'invalid': 'data'}
        result = self.service.extract_binance_percentage(invalid_ticker)
        self.assertFalse(result.is_valid)
        self.assertEqual(result.value, 0.0)
        
        # Invalid price calculation
        result = self.service.calculate_percentage_change(50000.0, 0.0)  # Division by zero
        self.assertFalse(result.is_valid)
        
        # Negative base price
        result = self.service.calculate_percentage_change(50000.0, -1000.0)
        self.assertFalse(result.is_valid)
    
    def test_decimal_precision(self):
        """Test decimal precision consistency"""
        result = self.service.calculate_percentage_change(50123.456, 48987.123)
        
        # Should be rounded to 2 decimal places
        self.assertEqual(len(str(result.value).split('.')[-1]), 2)
        
        # Formatted display should also have consistent precision
        formatted = self.service.format_percentage_display(result)
        percentage_part = formatted.split('%')[0]
        if '.' in percentage_part:
            decimal_places = len(percentage_part.split('.')[-1])
            self.assertEqual(decimal_places, 2)

class TestSystemIntegration(unittest.TestCase):
    """Test integration with existing system components"""
    
    @patch('services.market.market_service.get_binance_futures_exchange')
    def test_watchlist_integration(self, mock_exchange):
        """Test integration with watchlist commands"""
        # Mock exchange response
        mock_exchange_instance = Mock()
        mock_exchange.return_value = mock_exchange_instance
        mock_exchange_instance.fetch_ticker.return_value = {
            'percentage': 5.25,
            'last': 50000.0,
            'open': 47500.0,
            'quoteVolume': 1000000000.0
        }
        
        # Test that watchlist would get consistent percentage
        from services.market.percentage_calculation_service import get_percentage_service
        service = get_percentage_service()
        
        ticker = mock_exchange_instance.fetch_ticker('BTC/USDT:USDT')
        result = service.extract_binance_percentage(ticker)
        
        self.assertTrue(result.is_valid)
        self.assertEqual(result.value, 5.25)
        self.assertEqual(result.method, CalculationMethod.ROLLING_24H)
    
    def test_alert_integration(self):
        """Test integration with price alert system"""
        service = get_percentage_service()
        
        # Simulate market data that would trigger an alert
        market_data = {
            'BTCUSDT': {
                'daily_change': 6.5,  # Above 5% threshold
                'volume_24h': 1000000000.0,
                'percentage_source': 'binance_futures',
                'percentage_method': 'rolling_24h'
            }
        }
        
        # Verify the percentage would be processed consistently
        daily_change = market_data['BTCUSDT']['daily_change']
        self.assertGreater(daily_change, 5.0)  # Would trigger alert
        
        # Verify source and method are tracked
        self.assertEqual(market_data['BTCUSDT']['percentage_source'], 'binance_futures')
        self.assertEqual(market_data['BTCUSDT']['percentage_method'], 'rolling_24h')

def run_consistency_tests():
    """Run all consistency tests"""
    print("🧪 Running Percentage Calculation Consistency Tests...")
    print("=" * 60)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestPercentageConsistency))
    suite.addTests(loader.loadTestsFromTestCase(TestSystemIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✅ All consistency tests passed!")
        print(f"📊 Tests run: {result.testsRun}")
    else:
        print("❌ Some tests failed!")
        print(f"📊 Tests run: {result.testsRun}")
        print(f"❌ Failures: {len(result.failures)}")
        print(f"💥 Errors: {len(result.errors)}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    run_consistency_tests()
