#!/usr/bin/env python3
"""
Simple Data Service for Trading Bot
Fetches account, positions, and orders data every 60 seconds
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List

logger = logging.getLogger(__name__)

def safe_float(value, default=0):
    """Safely convert value to float"""
    if value is None or value == '':
        return default
    try:
        return float(value)
    except (ValueError, TypeError):
        return default

class TradingDataService:
    def __init__(self, trading_service=None):
        self.trading_service = trading_service
        self.last_update = None
        self.cached_data = {
            'account': {},
            'positions': [],
            'orders': []
        }
        self.update_interval = 60  # seconds
        self.running = False
        self.update_task = None

    def set_trading_service(self, trading_service):
        """Set trading service instance"""
        self.trading_service = trading_service

    async def start_data_updates(self):
        """Start periodic data updates"""
        if self.running:
            return
        
        self.running = True
        self.update_task = asyncio.create_task(self._update_loop())
        logger.info("🔄 Trading data service started - updating every 60 seconds")

    async def stop_data_updates(self):
        """Stop periodic data updates"""
        self.running = False
        if self.update_task:
            self.update_task.cancel()
            try:
                await self.update_task
            except asyncio.CancelledError:
                pass
        logger.info("⏹️ Trading data service stopped")

    async def _update_loop(self):
        """Main update loop"""
        while self.running:
            try:
                await self.update_all_data()
                await asyncio.sleep(self.update_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Error in data update loop: {e}")
                await asyncio.sleep(5)  # Short delay before retry

    async def update_all_data(self):
        """Update all trading data"""
        if not self.trading_service:
            logger.warning("⚠️ Trading service not available")
            return

        try:
            # Update account info
            account_result = self.trading_service.get_account_balance()
            if account_result.get('success'):
                self.cached_data['account'] = account_result
            
            # Update positions
            positions_result = self.trading_service.get_positions()
            if positions_result.get('success'):
                # Filter only positions with non-zero amounts
                active_positions = []
                for pos in positions_result.get('positions', []):
                    try:
                        contracts_value = pos.get('contracts')
                        contracts_float = safe_float(contracts_value)
                        if contracts_float != 0:
                            # Determine position side based on contracts sign
                            contracts = contracts_float

                            # According to Binance API: positive positionAmt = LONG, negative = SHORT
                            # But let's also check the 'side' field from API for verification
                            api_side = pos.get('side', '').lower()
                            calculated_side = 'LONG' if contracts > 0 else 'SHORT'

                            # Use API side if available and valid, otherwise use calculated
                            if api_side in ['long', 'short']:
                                position_side = api_side.upper()
                            else:
                                position_side = calculated_side

                            # Debug logging to track position side calculation
                            symbol = pos.get('symbol', 'Unknown')
                            logger.info(f"Position {symbol}: contracts={contracts}, api_side={api_side}, calculated={calculated_side}, final_position_side={position_side}")

                            active_positions.append({
                                'symbol': symbol,
                                'side': api_side,
                                'position_side': position_side,
                                'size': abs(contracts),
                                'entry_price': safe_float(pos.get('entryPrice')),
                                'mark_price': safe_float(pos.get('markPrice')),
                                'unrealized_pnl': safe_float(pos.get('unrealizedPnl')),
                                'percentage': safe_float(pos.get('percentage'))
                            })
                    except Exception as e:
                        logger.error(f"❌ Error processing position {pos}: {e}")
                        continue
                self.cached_data['positions'] = active_positions
            
            # Update open orders
            orders_result = self.trading_service.get_open_orders()
            if orders_result.get('success'):
                formatted_orders = []
                for order in orders_result.get('orders', []):
                    # Extract position side from order info
                    position_side = order.get('info', {}).get('positionSide', '') or order.get('positionSide', '')

                    formatted_orders.append({
                        'id': order.get('id'),
                        'symbol': order.get('symbol'),
                        'side': order.get('side'),
                        'type': order.get('type'),
                        'position_side': position_side,
                        'amount': safe_float(order.get('amount')),
                        'price': safe_float(order.get('price')),
                        'status': order.get('status'),
                        'stop_price': safe_float(order.get('stopPrice')),
                        'trigger_price': safe_float(order.get('triggerPrice')),
                        'take_profit_price': safe_float(order.get('takeProfitPrice')),
                        'stop_loss_price': safe_float(order.get('stopLossPrice'))
                    })
                self.cached_data['orders'] = formatted_orders

            self.last_update = datetime.now(timezone.utc)
            logger.debug(f"📊 Data updated: {len(self.cached_data['positions'])} positions, {len(self.cached_data['orders'])} orders")

        except Exception as e:
            logger.error(f"❌ Error updating trading data: {e}")

    def get_account_data(self) -> Dict:
        """Get cached account data"""
        return self.cached_data.get('account', {})

    def get_positions_data(self) -> List[Dict]:
        """Get cached positions data"""
        return self.cached_data.get('positions', [])

    def get_orders_data(self) -> List[Dict]:
        """Get cached orders data"""
        return self.cached_data.get('orders', [])

    def _correlate_tp_sl_with_positions(self, positions: List[Dict], orders: List[Dict]) -> List[Dict]:
        """Correlate TP/SL orders with positions"""
        enhanced_positions = []

        for pos in positions:
            pos_symbol = pos.get('symbol', '')
            pos_side = pos.get('position_side', '').upper()

            # Find TP/SL orders for this position
            tp_price = 0
            sl_price = 0

            for order in orders:
                order_symbol = order.get('symbol', '')
                order_type = order.get('type', '').lower()

                # Match symbol
                if order_symbol != pos_symbol:
                    continue

                # Get order price
                order_price = order.get('stop_price', 0) or order.get('trigger_price', 0) or order.get('price', 0)

                # Determine if this order is TP or SL for this position
                if 'take_profit' in order_type:
                    tp_price = order_price
                elif 'stop' in order_type and 'take_profit' not in order_type:
                    sl_price = order_price

            # Add TP/SL info to position
            enhanced_pos = pos.copy()
            enhanced_pos['take_profit_price'] = tp_price
            enhanced_pos['stop_loss_price'] = sl_price
            enhanced_positions.append(enhanced_pos)

        return enhanced_positions

    def get_summary_data(self) -> Dict:
        """Get summary of all data for status dashboard"""
        account = self.get_account_data()
        positions = self.get_positions_data()
        orders = self.get_orders_data()

        # Correlate TP/SL orders with positions
        enhanced_positions = self._correlate_tp_sl_with_positions(positions, orders)

        # Calculate totals
        total_unrealized_pnl = sum(pos.get('unrealized_pnl', 0) for pos in enhanced_positions)

        return {
            'account': {
                'total_balance': account.get('total_usdt', 0),
                'free_balance': account.get('free_usdt', 0),
                'margin_used': account.get('used_usdt', 0)
            },
            'positions': {
                'count': len(enhanced_positions),
                'total_unrealized_pnl': total_unrealized_pnl,
                'list': enhanced_positions
            },
            'orders': {
                'count': len(orders),
                'list': orders
            },
            'last_update': self.last_update.isoformat() if self.last_update else None
        }

    def is_data_fresh(self, max_age_seconds: int = 120) -> bool:
        """Check if cached data is fresh"""
        if not self.last_update:
            return False
        
        age = (datetime.now(timezone.utc) - self.last_update).total_seconds()
        return age <= max_age_seconds

    async def force_update(self):
        """Force immediate data update"""
        await self.update_all_data()

# Global instance
trading_data_service = TradingDataService()
