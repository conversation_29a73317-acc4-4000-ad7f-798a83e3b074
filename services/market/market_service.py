import logging
import time
import asyncio
import threading
import aiohttp
from typing import Dict, List, Any, Optional, Union, Callable, Tuple
import sqlite3
from datetime import datetime, timezone
import ccxt

from services.core.symbol_service import normalize_symbol, is_valid_symbol, format_symbol_for_exchange
from services.core.error_service import handle_service_errors, retry_with_backoff
from services.data.cache_service import (
    get_cache_service, MARKET_DATA_TTL, PRICE_TTL
)
from services.core.http_client_service import get_http_client
from utils.config import load_config
from utils.symbol_mappings import get_coingecko_id, is_symbol_supported

logger = logging.getLogger(__name__)

COINGECKO_API_URL = "https://api.coingecko.com/api/v3"
FEAR_GREED_API_URL = "https://api.alternative.me/fng/"

_binance_futures_exchange = None
_binance_spot_exchange = None

def get_binance_futures_exchange():
    global _binance_futures_exchange
    if _binance_futures_exchange is None:
        _binance_futures_exchange = ccxt.binance({
            'timeout': 30000,
            'enableRateLimit': True,
            'sandbox': False,
            'options': {
                'defaultType': 'future'
            }
        })
    return _binance_futures_exchange

def get_binance_spot_exchange():
    global _binance_spot_exchange
    if _binance_spot_exchange is None:
        _binance_spot_exchange = ccxt.binance({
            'timeout': 30000,
            'enableRateLimit': True,
            'sandbox': False,
            'options': {
                'defaultType': 'spot'
            }
        })
    return _binance_spot_exchange

class MarketService:
    def __init__(self, db_path: str = "chartfix.db"):
        self.db_path = db_path

        self.cache_service = get_cache_service()
        self.market_cache = self.cache_service
        self.price_cache = self.cache_service

        self.price_cache_lock = threading.RLock()
        self.alert_lock = threading.RLock()



        self.price_data = {}
        self.last_alert_times = {}
        self.recent_alerts = {}

        self.price_update_callbacks = []

        self._init_db()

    def _init_db(self):
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()



            cursor.execute('''
                CREATE TABLE IF NOT EXISTS alert_cooldowns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT,
                    timeframe TEXT,
                    last_alert_time INTEGER,
                    UNIQUE(symbol, timeframe)
                )
            ''')

            conn.commit()

    @handle_service_errors
    async def get_price(self, symbol: str) -> Optional[float]:
        symbol = normalize_symbol(symbol)

        if not is_valid_symbol(symbol):
            logger.warning(f"Symbol không hợp lệ: {symbol}")
            return None

        with self.price_cache_lock:
            cached_price = self.price_cache.get(f"price_{symbol}")
            if cached_price is not None:
                return cached_price

        try:
            exchange = get_binance_futures_exchange()
            formatted_symbol = format_symbol_for_exchange(symbol)

            loop = asyncio.get_event_loop()
            ticker = await loop.run_in_executor(None, exchange.fetch_ticker, formatted_symbol)

            if ticker and 'last' in ticker and ticker['last'] is not None:
                try:
                    price = float(ticker['last'])
                    with self.price_cache_lock:
                        self.price_cache.set(f"price_{symbol}", price, ttl=PRICE_TTL)
                    return price
                except (ValueError, TypeError):
                    logger.warning(f"Invalid price data for {symbol}: {ticker['last']}")
                    return None
            else:
                logger.warning(f"No price data in ticker for {symbol}")
                return None

        except (ccxt.NetworkError, ccxt.RequestTimeout, ccxt.ExchangeError) as e:
            logger.error(f"CCXT error fetching price for {symbol}: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error fetching price for {symbol}: {e}")
            return None

    @handle_service_errors
    async def get_prices(self, symbols: List[str]) -> Dict[str, Union[float, str]]:
        logger.info(f"[MARKET-SERVICE] get_prices called with symbols: {symbols}")
        result = {}
        valid_symbols = []

        for symbol in symbols:
            normalized = normalize_symbol(symbol)
            if is_valid_symbol(normalized):
                valid_symbols.append(normalized)
            else:
                logger.warning(f"[MARKET-SERVICE] Invalid symbol: {normalized}")

        logger.info(f"[MARKET-SERVICE] Valid symbols after normalization: {valid_symbols}")

        if not valid_symbols:
            logger.warning(f"[MARKET-SERVICE] No valid symbols to process")
            return result

        cache_hits = []
        cache_misses = []
        with self.price_cache_lock:
            for symbol in valid_symbols[:]:
                cached_price = self.price_cache.get(f"price_{symbol}")
                if cached_price is not None:
                    result[symbol] = cached_price
                    cache_hits.append(f"{symbol}={cached_price}")
                    valid_symbols.remove(symbol)
                else:
                    cache_misses.append(symbol)

        logger.info(f"[MARKET-SERVICE] Cache hits: {cache_hits}")
        logger.info(f"[MARKET-SERVICE] Cache misses (need API fetch): {cache_misses}")

        if not valid_symbols:
            logger.info(f"[MARKET-SERVICE] All prices served from cache: {result}")
            return result

        try:
            logger.info(f"[MARKET-SERVICE] Fetching from Binance Futures API for symbols: {valid_symbols}")
            exchange = get_binance_futures_exchange()

            formatted_symbols = []
            symbol_mapping = {}

            for symbol in valid_symbols:
                try:
                    formatted = format_symbol_for_exchange(symbol)
                    formatted_symbols.append(formatted)
                    symbol_mapping[formatted] = symbol
                    logger.debug(f"[MARKET-SERVICE] Symbol mapping: {symbol} -> {formatted}")
                except Exception as e:
                    logger.warning(f"[MARKET-SERVICE] Could not format symbol {symbol}: {e}")

            logger.info(f"[MARKET-SERVICE] Formatted symbols for API: {formatted_symbols}")

            if not formatted_symbols:
                logger.warning(f"[MARKET-SERVICE] No formatted symbols for API call")
                return result

            logger.info(f"[MARKET-SERVICE] Calling exchange.fetch_tickers({formatted_symbols})")
            loop = asyncio.get_event_loop()
            tickers = await loop.run_in_executor(None, exchange.fetch_tickers, formatted_symbols)
            logger.info(f"[MARKET-SERVICE] API response received, {len(tickers)} tickers")

            api_results = []
            cache_updates = []
            processing_errors = []

            for formatted_symbol, ticker in tickers.items():
                clean_symbol = formatted_symbol.split(':')[0] if ':' in formatted_symbol else formatted_symbol
                original_symbol = symbol_mapping.get(clean_symbol) or symbol_mapping.get(formatted_symbol)

                logger.debug(f"[MARKET-SERVICE] Processing ticker: {formatted_symbol} -> {clean_symbol} -> {original_symbol}")
                logger.debug(f"[MARKET-SERVICE] Ticker data: {ticker}")

                if original_symbol and ticker and 'last' in ticker and ticker['last'] is not None:
                    try:
                        price = float(ticker['last'])
                        result[original_symbol] = price
                        api_results.append(f"{original_symbol}={price}")
                    except (ValueError, TypeError):
                        logger.warning(f"Invalid price data for {original_symbol}: {ticker['last']}")
                        continue

                        with self.price_cache_lock:
                            self.price_cache.set(f"price_{original_symbol}", price, ttl=PRICE_TTL)
                            cache_updates.append(f"{original_symbol}={price}")
                    except (ValueError, TypeError) as e:
                        error_msg = f"Invalid price data for {original_symbol}: {ticker.get('last')} - {e}"
                        logger.warning(f"[MARKET-SERVICE] {error_msg}")
                        processing_errors.append(error_msg)
                else:
                    error_msg = f"Failed to process ticker for {formatted_symbol}: original_symbol={original_symbol}, ticker_valid={ticker is not None}"
                    logger.warning(f"[MARKET-SERVICE] {error_msg}")
                    processing_errors.append(error_msg)

            logger.info(f"[MARKET-SERVICE] API results: {api_results}")
            logger.info(f"[MARKET-SERVICE] Cache updates: {cache_updates}")
            if processing_errors:
                logger.warning(f"[MARKET-SERVICE] Processing errors: {processing_errors}")

            missing_symbols = [s for s in valid_symbols if s not in result]
            if missing_symbols:
                logger.warning(f"[MARKET-SERVICE] No data received for symbols: {missing_symbols}")

        except (ccxt.NetworkError, ccxt.RequestTimeout, ccxt.ExchangeError) as e:
            logger.error(f"[MARKET-SERVICE] CCXT error fetching prices for {valid_symbols}: {e}")
        except Exception as e:
            logger.error(f"[MARKET-SERVICE] Unexpected error fetching prices for {valid_symbols}: {e}")
            import traceback
            logger.error(f"[MARKET-SERVICE] Traceback: {traceback.format_exc()}")

        logger.info(f"[MARKET-SERVICE] Final result returned: {result}")
        return result

    def get_current_price(self, symbol: str) -> Optional[float]:
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self.get_price(symbol))
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"Error getting current price for {symbol}: {e}")
            return None

    def get_all_prices(self) -> Dict[str, float]:
        try:
            from utils.config import get_watchlist_symbols
            symbols = get_watchlist_symbols()

            if not symbols:
                return {}

            result = {}

            for symbol in symbols:
                normalized = normalize_symbol(symbol)

                if normalized in self.price_data:
                    current_price = self.price_data[normalized].get('current', 0)
                    if current_price > 0:
                        result[normalized] = current_price
                        continue

                with self.price_cache_lock:
                    cached_price = self.price_cache.get(f"price_{normalized}")
                    if cached_price is not None and cached_price > 0:
                        result[normalized] = cached_price

            return result
        except Exception as e:
            logger.error(f"Error getting all prices: {e}")
            return {}

    @handle_service_errors
    @retry_with_backoff(max_retries=1)
    async def fetch_market_overview(self) -> Dict[str, Any]:
        cache_key = "market_overview"

        cached_data = self.market_cache.get(cache_key)
        if cached_data:
            return cached_data

        try:
            coingecko_task = asyncio.create_task(self.fetch_coingecko_market_data())
            fear_greed_task = asyncio.create_task(self.fetch_fear_greed_index())

            await asyncio.gather(coingecko_task, fear_greed_task)

            market_data = coingecko_task.result()

            fear_greed = fear_greed_task.result()

            result = {
                "market_data": market_data,
                "fear_greed": fear_greed
            }

            self.market_cache.set(cache_key, result, ttl=MARKET_DATA_TTL)
            return result
        except Exception as e:
            logger.error(f"Lỗi trong fetch_market_overview: {e}")
            return {
                "market_data": {'top_coins': [], 'global': {}},
                "fear_greed": {'value': 0, 'classification': 'Unknown', 'timestamp': 0}
            }

    @handle_service_errors
    @retry_with_backoff(max_retries=1)
    async def fetch_coingecko_market_data(self, vs_currency: str = "usd", per_page: int = 100) -> Dict:
        """
        Lấy dữ liệu thị trường từ CoinGecko với caching.

        Args:
            vs_currency: Đơn vị tiền tệ (usd, eur, etc.)
            per_page: Số lượng coin trả về

        Returns:
            Dictionary chứa dữ liệu thị trường
        """
        cache_key = f"coingecko_market_{vs_currency}_{per_page}"

        # Kiểm tra cache
        cached_data = self.market_cache.get(cache_key)
        if cached_data:
            return cached_data

        result = {
            'top_coins': [],
            'global': {
                'total_market_cap': 0,
                'total_volume_24h': 0,
                'market_cap_change_24h': 0,
                'dominance': {'BTC': 0, 'ETH': 0}
            },
        }

        try:
            http_client = await get_http_client()

            # Lấy dữ liệu thị trường
            url = f"{COINGECKO_API_URL}/coins/markets?vs_currency={vs_currency}&order=market_cap_desc&per_page={per_page}&page=1&sparkline=false"
            logger.info(f"Fetching CoinGecko markets: {url}")

            async with http_client.request('GET', url) as response:
                if response.status == 200:
                    coins_data = await response.json()

                    # Define stablecoins to filter out
                    stablecoins = {
                        'USDT', 'USDC', 'BUSD', 'DAI', 'TUSD', 'USDD', 'FDUSD',
                        'FRAX', 'LUSD', 'USDP', 'GUSD', 'HUSD', 'SUSD',
                        'USDN', 'RSV', 'DUSD', 'OUSD', 'USTC', 'USDK',
                        'BSC-USD', 'PYUSD', 'USDJ', 'USDE', 'USDB'
                    }

                    # Xử lý dữ liệu top coins (filter out stablecoins)
                    for coin in coins_data:
                        symbol = coin.get('symbol', '').upper()

                        # Skip stablecoins
                        if symbol in stablecoins:
                            logger.debug(f"Filtering out stablecoin: {symbol}")
                            continue

                        result['top_coins'].append({
                            'id': coin.get('id', ''),
                            'symbol': symbol,
                            'name': coin.get('name', ''),
                            'current_price': coin.get('current_price', 0),
                            'market_cap': coin.get('market_cap', 0),
                            'market_cap_rank': coin.get('market_cap_rank', 0),
                            'price_change_24h': coin.get('price_change_24h', 0),
                            'price_change_percentage_24h': coin.get('price_change_percentage_24h', 0),
                            'total_volume': coin.get('total_volume', 0),  # Ensure this field is mapped correctly
                            'volume_24h': coin.get('total_volume', 0),   # Keep for backward compatibility
                            'image': coin.get('image', '')
                        })

            # Lấy dữ liệu global
            global_url = f"{COINGECKO_API_URL}/global"
            logger.info(f"Fetching CoinGecko global data: {global_url}")

            async with http_client.request('GET', global_url) as response:
                if response.status == 200:
                    global_data = await response.json()
                    data = global_data.get('data', {})

                    # Xử lý dữ liệu global
                    result['global'] = {
                        'total_market_cap': data.get('total_market_cap', {}).get(vs_currency, 0),
                        'total_volume_24h': data.get('total_volume', {}).get(vs_currency, 0),
                        'market_cap_change_24h': data.get('market_cap_change_percentage_24h_usd', 0),
                        'dominance': {
                            'BTC': data.get('market_cap_percentage', {}).get('btc', 0),
                            'ETH': data.get('market_cap_percentage', {}).get('eth', 0)
                        }
                    }

            # Lưu vào cache
            self.market_cache.set(cache_key, result, ttl=MARKET_DATA_TTL)
            return result
        except aiohttp.ClientConnectorError as e:
            logger.error(f"Error connecting to CoinGecko API: {e}")
            return result
        except aiohttp.ClientResponseError as e:
            logger.error(f"CoinGecko API response error: {e.status} - {e.message}")
            return result
        except aiohttp.ClientError as e:
            logger.error(f"CoinGecko API client error: {e}")
            return result
        except asyncio.TimeoutError:
            logger.error("CoinGecko API request timed out")
            return result
        except Exception as e:
            logger.error(f"Error fetching CoinGecko market data: {e}")
            return result

    @handle_service_errors
    @retry_with_backoff(max_retries=1)
    async def fetch_fear_greed_index(self) -> Dict:
        """
        Lấy chỉ số Fear & Greed với caching.

        Returns:
            Dictionary chứa dữ liệu Fear & Greed
        """
        cache_key = "fear_greed_index"

        # Kiểm tra cache
        cached_data = self.market_cache.get(cache_key)
        if cached_data:
            return cached_data

        try:
            http_client = await get_http_client()
            async with http_client.request('GET', FEAR_GREED_API_URL) as response:
                if response.status == 200:
                    data = await response.json()

                    if 'data' in data and len(data['data']) > 0:
                        fng_data = data['data'][0]
                        result = {
                            'value': int(fng_data.get('value', 0)),
                            'classification': fng_data.get('value_classification', 'Unknown'),
                            'timestamp': int(fng_data.get('timestamp', 0))
                        }

                        # Lưu vào cache
                        self.market_cache.set(cache_key, result, ttl=MARKET_DATA_TTL)
                        return result

            return {'value': 0, 'classification': 'Unknown', 'timestamp': 0}
        except Exception as e:
            logger.error(f"Error fetching Fear & Greed Index: {e}")
            return {'value': 0, 'classification': 'Unknown', 'timestamp': 0}

    #
    # Phần 3: Dịch vụ theo dõi biến động giá
    #

    def register_price_update_callback(self, callback: Callable[[str, float], None]) -> int:
        """
        Đăng ký callback cho cập nhật giá.

        Args:
            callback: Hàm callback nhận symbol và giá mới

        Returns:
            ID của callback
        """
        self.price_update_callbacks.append(callback)
        return len(self.price_update_callbacks) - 1

    def unregister_price_update_callback(self, callback_id: int) -> None:
        """
        Hủy đăng ký callback cho cập nhật giá.

        Args:
            callback_id: ID của callback cần hủy
        """
        if 0 <= callback_id < len(self.price_update_callbacks):
            self.price_update_callbacks[callback_id] = None



    def update_price(self, symbol: str, price: float) -> None:
        """
        Cập nhật giá và kiểm tra biến động.

        Args:
            symbol: Symbol cần cập nhật
            price: Giá mới
        """
        if not symbol or not isinstance(price, (int, float)) or price <= 0:
            return

        symbol = normalize_symbol(symbol)
        current_time = int(time.time())

        # Khởi tạo dữ liệu nếu chưa có
        if symbol not in self.price_data:
            self.price_data[symbol] = {
                'current': 0.0,
                'history': [],
                'last_update': 0
            }

        # Lấy giá trước đó
        previous_price = self.price_data[symbol]['current']

        # Cập nhật giá mới
        self.price_data[symbol]['current'] = price
        self.price_data[symbol]['last_update'] = current_time

        # Thêm vào lịch sử giá
        self.price_data[symbol]['history'].append((price, current_time))

        # Giới hạn kích thước lịch sử
        max_history = 60  # Lưu 60 điểm giá
        if len(self.price_data[symbol]['history']) > max_history:
            self.price_data[symbol]['history'] = self.price_data[symbol]['history'][-max_history:]

        # Lưu vào cache
        with self.price_cache_lock:
            self.price_cache.set(symbol, price)

        # Thông báo cho các callback
        for callback in self.price_update_callbacks:
            if callback is not None:
                try:
                    callback(symbol, price)
                except Exception as e:
                    logger.error(f"Error in price update callback: {e}")















    def get_price_history(self, symbol: str) -> List[Tuple[float, int]]:
        """
        Lấy lịch sử giá cho một symbol.

        Args:
            symbol: Symbol cần lấy lịch sử

        Returns:
            Danh sách các tuple (giá, thời gian)
        """
        symbol = normalize_symbol(symbol)

        if symbol in self.price_data:
            return self.price_data[symbol].get('history', [])

        return []



    def reset_daily_data(self) -> None:
        """
        Reset dữ liệu hàng ngày.
        """
        logger.info("Resetting daily price data")
        # Reset price data
        self.price_data = {}
        # Reset alert times
        self.last_alert_times = {}
        self.recent_alerts = {}
        # Clear caches
        with self.price_cache_lock:
            self.price_cache.clear()
        self.market_cache.clear()

    def _fetch_ohlcv_data(self, symbol: str, timeframe: str = '1d', limit: int = 2) -> Any:
        """Fetch OHLCV data for a symbol from Binance Futures"""
        try:
            import pandas as pd

            # Normalize symbol
            symbol = normalize_symbol(symbol)
            if not is_valid_symbol(symbol):
                return pd.DataFrame()

            # Format symbol for exchange
            formatted_symbol = format_symbol_for_exchange(symbol)

            # Fetch from exchange
            exchange = get_binance_futures_exchange()
            ohlcv = exchange.fetch_ohlcv(formatted_symbol, timeframe, limit=limit)

            if not ohlcv:
                return pd.DataFrame()

            # Convert to DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')

            return df

        except Exception as e:
            logger.error(f"Error fetching OHLCV data for {symbol}: {e}")
            import pandas as pd
            return pd.DataFrame()

    async def get_watchlist_data(self) -> Dict[str, Any]:
        """Get watchlist data with current prices"""
        try:
            from utils.config import get_watchlist_symbols
            symbols = get_watchlist_symbols()

            if not symbols:
                logger.warning("No watchlist symbols configured")
                return {
                    'success': False,
                    'error': 'No watchlist symbols configured',
                    'prices': {}
                }

            # Get current prices for all symbols
            prices = await self.get_prices(symbols)

            if not prices:
                logger.warning("No price data received for watchlist symbols")
                return {
                    'success': False,
                    'error': 'No price data available',
                    'prices': {}
                }

            logger.info(f"Successfully fetched watchlist data for {len(prices)} symbols")
            return {
                'success': True,
                'prices': prices,
                'symbols': symbols
            }

        except Exception as e:
            logger.error(f"Error getting watchlist data: {e}")
            return {
                'success': False,
                'error': str(e),
                'prices': {}
            }

    async def get_comprehensive_price_data(self, symbol: str) -> Dict[str, Any]:
        """
        Get comprehensive price data for a symbol including price, volume, market cap, and percentage changes.

        Args:
            symbol: Trading symbol (e.g., BTCUSDT)

        Returns:
            Dictionary containing comprehensive price data
        """
        try:
            from services.core.symbol_service import smart_normalize_symbol

            # Normalize symbol
            symbol = smart_normalize_symbol(symbol)

            # Cache key for comprehensive data
            cache_key = f"comprehensive_price_{symbol}"

            # Check cache first
            cached_data = self.market_cache.get(cache_key)
            if cached_data:
                return cached_data

            # Get data from multiple sources
            logger.info(f"Fetching comprehensive data for {symbol}")
            binance_data = await self._fetch_binance_ticker_data(symbol)
            coingecko_data = await self._fetch_coingecko_coin_data(symbol)

            logger.info(f"Binance data available: {bool(binance_data)}")
            logger.info(f"CoinGecko data available: {bool(coingecko_data)}")

            # Combine data with intelligent fallbacks
            current_price = (
                binance_data.get('last', 0) or
                binance_data.get('close', 0) or
                coingecko_data.get('current_price', 0) or
                0
            )

            volume_24h = (
                binance_data.get('quoteVolume', 0) or
                binance_data.get('baseVolume', 0) or
                coingecko_data.get('total_volume', 0) or
                0
            )

            price_change_24h = (
                binance_data.get('percentage', 0) or
                coingecko_data.get('price_change_percentage_24h', 0) or
                0
            )

            result = {
                'symbol': symbol,
                'name': coingecko_data.get('name', symbol.replace('USDT', '').upper()),
                'current_price': current_price,
                'high_24h': binance_data.get('high', 0),
                'low_24h': binance_data.get('low', 0),
                'volume_24h': volume_24h,
                'price_change_1h': coingecko_data.get('price_change_percentage_1h_in_currency', 0),
                'price_change_24h': price_change_24h,
                'price_change_7d': coingecko_data.get('price_change_percentage_7d_in_currency', 0),
                'price_change_30d': coingecko_data.get('price_change_percentage_30d_in_currency', 0),
                'market_cap': coingecko_data.get('market_cap', 0),
                'ath': coingecko_data.get('ath', 0),
                'ath_change_percentage': coingecko_data.get('ath_change_percentage', 0),
                'success': True
            }

            # Log data quality for debugging
            missing_critical = []
            if result['current_price'] == 0:
                missing_critical.append('current_price')
            if result['price_change_24h'] == 0:
                missing_critical.append('price_change_24h')
            if result['volume_24h'] == 0:
                missing_critical.append('volume_24h')

            missing_extended = []
            if result['price_change_7d'] == 0:
                missing_extended.append('price_change_7d')
            if result['price_change_30d'] == 0:
                missing_extended.append('price_change_30d')
            if result['ath'] == 0:
                missing_extended.append('ath')
            if result['market_cap'] == 0:
                missing_extended.append('market_cap')

            if missing_critical:
                logger.warning(f"Missing critical data for {symbol}: {missing_critical}")
            if missing_extended:
                logger.info(f"Missing extended data for {symbol}: {missing_extended}")
            else:
                logger.info(f"Complete data retrieved for {symbol}")

            # Cache the result
            self.market_cache.set(cache_key, result, ttl=60)  # Cache for 1 minute

            return result

        except Exception as e:
            logger.error(f"Error getting comprehensive price data for {symbol}: {e}")
            return {
                'symbol': symbol,
                'success': False,
                'error': str(e)
            }

    async def _fetch_binance_ticker_data(self, symbol: str) -> Dict[str, Any]:
        """Fetch detailed ticker data from Binance FUTURES ONLY - No Spot fallback for trading safety"""
        try:
            from services.core.symbol_service import format_symbol_for_exchange

            exchange = get_binance_futures_exchange()
            formatted_symbol = format_symbol_for_exchange(symbol)

            logger.info(f"Fetching Binance FUTURES ticker for {symbol} -> {formatted_symbol}")

            loop = asyncio.get_event_loop()
            ticker = await loop.run_in_executor(None, exchange.fetch_ticker, formatted_symbol)

            if ticker:
                logger.info(f"Binance FUTURES ticker data retrieved for {formatted_symbol}")
                logger.debug(f"Binance ticker fields: {list(ticker.keys())}")

                # Validate critical fields
                critical_fields = ['last', 'high', 'low', 'baseVolume', 'quoteVolume', 'percentage']
                missing_fields = [field for field in critical_fields if field not in ticker or ticker[field] is None]
                if missing_fields:
                    logger.warning(f"Binance ticker missing fields for {formatted_symbol}: {missing_fields}")

                return ticker
            else:
                logger.warning(f"Empty ticker data from Binance FUTURES for {formatted_symbol}")
                return {}

        except Exception as e:
            logger.error(f"Error fetching Binance FUTURES ticker data for {symbol}: {e}")
            logger.warning(f"⚠️ FUTURES data unavailable for {symbol} - NO SPOT FALLBACK (Trading Safety)")
            return {}

    async def _fetch_coingecko_coin_data(self, symbol: str) -> Dict[str, Any]:
        """Fetch coin data from CoinGecko for additional metrics"""
        try:
            # Get CoinGecko coin ID using centralized mapping utility
            coin_id = get_coingecko_id(symbol)
            base_symbol = symbol.replace('USDT', '').lower()

            logger.info(f"Fetching CoinGecko data for {symbol} -> {base_symbol} -> {coin_id}")

            # Check if symbol is supported
            if not is_symbol_supported(symbol):
                logger.warning(f"Symbol '{symbol}' not in supported mappings, using fallback: {coin_id}")

            url = f"{COINGECKO_API_URL}/coins/{coin_id}?localization=false&tickers=false&market_data=true&community_data=false&developer_data=false&sparkline=false"

            http_client = await get_http_client()
            async with http_client.request('GET', url) as response:
                logger.info(f"CoinGecko API response status for {coin_id}: {response.status}")

                if response.status == 200:
                    data = await response.json()
                    market_data = data.get('market_data', {})

                    logger.debug(f"CoinGecko raw data for {coin_id}: {data.get('name', 'Unknown')}")

                    # Extract data with safe fallbacks
                    result = {
                        'name': data.get('name', ''),
                        'current_price': self._safe_get_usd_value(market_data, 'current_price'),
                        'market_cap': self._safe_get_usd_value(market_data, 'market_cap'),
                        'total_volume': self._safe_get_usd_value(market_data, 'total_volume'),
                        'price_change_percentage_1h_in_currency': self._safe_get_usd_value(market_data, 'price_change_percentage_1h_in_currency'),
                        'price_change_percentage_24h': market_data.get('price_change_percentage_24h', 0) or 0,
                        'price_change_percentage_7d_in_currency': self._safe_get_usd_value(market_data, 'price_change_percentage_7d_in_currency'),
                        'price_change_percentage_30d_in_currency': self._safe_get_usd_value(market_data, 'price_change_percentage_30d_in_currency'),
                        'ath': self._safe_get_usd_value(market_data, 'ath'),
                        'ath_change_percentage': self._safe_get_usd_value(market_data, 'ath_change_percentage')
                    }

                    # Log data quality
                    missing_fields = [k for k, v in result.items() if v == 0 and k != 'name']
                    if missing_fields:
                        logger.warning(f"CoinGecko missing data for {coin_id}: {missing_fields}")
                    else:
                        logger.info(f"CoinGecko data complete for {coin_id}")

                    return result

                elif response.status == 404:
                    logger.warning(f"CoinGecko: Coin ID '{coin_id}' not found (404)")
                    # Try alternative search if direct ID fails
                    return await self._search_coingecko_alternative(base_symbol, http_client)
                else:
                    logger.error(f"CoinGecko API error for {coin_id}: HTTP {response.status}")

            return {}

        except Exception as e:
            logger.error(f"Error fetching CoinGecko data for {symbol}: {e}")
            return {}

    def _safe_get_usd_value(self, market_data: dict, field: str) -> float:
        """Safely extract USD value from CoinGecko market data"""
        try:
            value = market_data.get(field, {})
            if isinstance(value, dict):
                return float(value.get('usd', 0) or 0)
            elif isinstance(value, (int, float)):
                return float(value or 0)
            else:
                return 0.0
        except (ValueError, TypeError, AttributeError):
            return 0.0

    async def _search_coingecko_alternative(self, base_symbol: str, http_client) -> Dict[str, Any]:
        """Search for coin using CoinGecko search API as fallback"""
        try:
            search_url = f"{COINGECKO_API_URL}/search?query={base_symbol}"
            logger.info(f"Searching CoinGecko for alternative: {base_symbol}")

            async with http_client.request('GET', search_url) as response:
                if response.status == 200:
                    search_data = await response.json()
                    coins = search_data.get('coins', [])

                    # Look for exact symbol match
                    for coin in coins:
                        if coin.get('symbol', '').lower() == base_symbol.lower():
                            coin_id = coin.get('id')
                            logger.info(f"Found alternative CoinGecko ID for {base_symbol}: {coin_id}")

                            # Fetch data using found ID
                            coin_url = f"{COINGECKO_API_URL}/coins/{coin_id}?localization=false&tickers=false&market_data=true&community_data=false&developer_data=false&sparkline=false"
                            async with http_client.request('GET', coin_url) as coin_response:
                                if coin_response.status == 200:
                                    data = await coin_response.json()
                                    market_data = data.get('market_data', {})

                                    return {
                                        'name': data.get('name', ''),
                                        'current_price': self._safe_get_usd_value(market_data, 'current_price'),
                                        'market_cap': self._safe_get_usd_value(market_data, 'market_cap'),
                                        'total_volume': self._safe_get_usd_value(market_data, 'total_volume'),
                                        'price_change_percentage_1h_in_currency': self._safe_get_usd_value(market_data, 'price_change_percentage_1h_in_currency'),
                                        'price_change_percentage_24h': market_data.get('price_change_percentage_24h', 0) or 0,
                                        'price_change_percentage_7d_in_currency': self._safe_get_usd_value(market_data, 'price_change_percentage_7d_in_currency'),
                                        'price_change_percentage_30d_in_currency': self._safe_get_usd_value(market_data, 'price_change_percentage_30d_in_currency'),
                                        'ath': self._safe_get_usd_value(market_data, 'ath'),
                                        'ath_change_percentage': self._safe_get_usd_value(market_data, 'ath_change_percentage')
                                    }

                    logger.warning(f"No exact symbol match found in CoinGecko search for {base_symbol}")
                else:
                    logger.error(f"CoinGecko search API error: HTTP {response.status}")

        except Exception as e:
            logger.error(f"Error in CoinGecko alternative search for {base_symbol}: {e}")

        return {}

    def shutdown(self) -> None:
        """
        Dọn dẹp tài nguyên khi tắt dịch vụ.
        """
        logger.info("Shutting down MarketService")
        # Clear callbacks
        self.price_update_callbacks = []
        # Clear caches
        with self.price_cache_lock:
            self.price_cache.clear()
        self.market_cache.clear()


# Singleton instance
_market_service = None
_instance_lock = threading.RLock()

def get_market_service() -> MarketService:
    """Lấy instance singleton của MarketService."""
    global _market_service
    with _instance_lock:
        if _market_service is None:
            _market_service = MarketService()
        return _market_service

# Các hàm tiện ích để tương thích ngược với mã cũ
@handle_service_errors
async def fetch_market_overview() -> Dict[str, Any]:
    """Lấy tổng quan thị trường."""
    service = get_market_service()
    return await service.fetch_market_overview()

@handle_service_errors
async def fetch_coingecko_market_data(vs_currency: str = "usd", per_page: int = 100) -> Dict:
    """Lấy dữ liệu thị trường từ CoinGecko."""
    service = get_market_service()
    return await service.fetch_coingecko_market_data(vs_currency, per_page)

@handle_service_errors
async def fetch_fear_greed_index() -> Dict:
    """Lấy chỉ số Fear & Greed."""
    service = get_market_service()
    return await service.fetch_fear_greed_index()

@handle_service_errors
def get_price(symbol: str) -> Optional[float]:
    """Lấy giá hiện tại cho một symbol."""
    service = get_market_service()

    # Lấy từ cache hoặc dữ liệu đã có
    symbol = normalize_symbol(symbol)
    if symbol in service.price_data:
        return service.price_data[symbol].get('current', 0)

    # Lấy từ cache
    with service.price_cache_lock:
        cached_price = service.price_cache.get(f"price_{symbol}")
        if cached_price is not None:
            return cached_price

    return None

@handle_service_errors
def get_all_prices() -> Dict[str, float]:
    """Lấy tất cả giá hiện tại."""
    service = get_market_service()
    return service.get_all_prices()

@handle_service_errors
def update_price(symbol: str, price: float) -> None:
    """Cập nhật giá và kiểm tra biến động."""
    service = get_market_service()
    service.update_price(symbol, price)

@handle_service_errors
def register_price_update_callback(callback: Callable[[str, float], None]) -> int:
    """Đăng ký callback cho cập nhật giá."""
    service = get_market_service()
    return service.register_price_update_callback(callback)



# Watchlist service compatibility functions
@handle_service_errors
async def get_watchlist_data() -> Dict[str, Any]:
    """Get watchlist data with current prices (compatibility function)"""
    service = get_market_service()
    return await service.get_watchlist_data()
