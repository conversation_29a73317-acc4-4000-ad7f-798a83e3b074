import logging
import threading
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional
from collections import defaultdict, deque

from services.data.cache_service import get_cache_service
from services.market.market_service import get_market_service
from services.core.error_service import handle_service_errors, retry_with_backoff
from utils.config import load_config

logger = logging.getLogger(__name__)

class TradingTimeService:
    def __init__(self):
        self.market_service = get_market_service()
        self.cache_service = get_cache_service()
        self.economic_calendar = None

        config = load_config()
        self.config = config.get('trading_time_analysis', {})

        self.volume_surge_threshold = self.config.get('volume_surge_threshold', 200)
        self.price_movement_threshold = self.config.get('price_movement_threshold', 5)
        self.top_coins_limit = self.config.get('top_coins_limit', 100)
        self.alert_thresholds = self.config.get('alert_thresholds', {})

        # Hardcoded market sessions per user preference (no config files)
        self.market_sessions = {
            'asian_high_volume': {
                'start': '00:00',
                'end': '04:00',
                'timezone': 'UTC',
                'description': 'Asian market peak liquidity'
            },
            'european_high_volume': {
                'start': '08:00',
                'end': '12:00',
                'timezone': 'UTC',
                'description': 'European market session'
            },
            'us_high_volume': {
                'start': '13:00',
                'end': '21:00',
                'timezone': 'UTC',
                'description': 'US market session'
            }
        }

        self.volume_history = defaultdict(lambda: deque(maxlen=20))
        self.price_history = defaultdict(lambda: deque(maxlen=20))

        # Daily cooldown mechanism - Reset at 0h UTC, max 1 alert per day per symbol per threshold
        self.daily_volume_alerts = {}  # {symbol_threshold: date_string} - tracks daily volume alerts
        self.daily_price_alerts = {}   # {symbol_threshold: date_string} - tracks daily price alerts
        self.recent_news_alerts = {}    # {event_key: timestamp}
        self.news_alert_cooldown = 1800   # 30 minutes

        self.alert_callbacks = []

    def register_alert_callback(self, callback):
        self.alert_callbacks.append(callback)

    def _should_trigger_volume_alert(self, symbol: str, threshold: float) -> bool:
        """
        Check if volume alert should be triggered based on daily cooldown rules.
        Maximum 1 alert per day per symbol per threshold, resets at 0h UTC.

        Args:
            symbol: Coin symbol (e.g., 'BTC')
            threshold: Volume surge threshold percentage

        Returns:
            True if alert should be sent, False if blocked by daily cooldown
        """
        # Get current UTC date string (YYYY-MM-DD)
        current_utc_date = datetime.now(timezone.utc).strftime('%Y-%m-%d')
        alert_key = f"{symbol}_{threshold}"

        # Check if this symbol+threshold already has alert today
        if alert_key in self.daily_volume_alerts:
            if self.daily_volume_alerts[alert_key] == current_utc_date:
                logger.debug(f"Skipping volume alert for {symbol} {threshold}% - already sent today ({current_utc_date})")
                return False

        # Record alert for today
        self.daily_volume_alerts[alert_key] = current_utc_date
        return True

    def _should_trigger_price_alert(self, symbol: str, threshold: float) -> bool:
        """
        Check if price movement alert should be triggered based on daily cooldown rules.
        Maximum 1 alert per day per symbol per threshold, resets at 0h UTC.

        Args:
            symbol: Coin symbol (e.g., 'BTC')
            threshold: Price movement threshold percentage

        Returns:
            True if alert should be sent, False if blocked by daily cooldown
        """
        # Get current UTC date string (YYYY-MM-DD)
        current_utc_date = datetime.now(timezone.utc).strftime('%Y-%m-%d')
        alert_key = f"{symbol}_{threshold}"

        # Check if this symbol+threshold already has alert today
        if alert_key in self.daily_price_alerts:
            if self.daily_price_alerts[alert_key] == current_utc_date:
                logger.debug(f"Skipping price alert for {symbol} {threshold}% - already sent today ({current_utc_date})")
                return False

        # Record alert for today
        self.daily_price_alerts[alert_key] = current_utc_date
        return True

    def _should_trigger_news_alert(self, event_title: str, currency: str) -> bool:
        """
        Check if news alert should be triggered based on deduplication rules.

        Args:
            event_title: Title of the economic event
            currency: Currency affected by the event

        Returns:
            True if alert should be sent, False if duplicate
        """
        current_time = int(time.time())
        event_key = f"{currency}_{event_title}"

        if event_key in self.recent_news_alerts:
            # Only send once per event - no time-based expiry for news events
            logger.debug(f"Skipping duplicate news alert: {event_title} for {currency}")
            return False

        self.recent_news_alerts[event_key] = current_time
        return True

    def _cleanup_old_alerts(self):
        """
        Clean up old alert records to prevent memory leaks.
        Called periodically to remove expired entries.
        """
        current_time = int(time.time())
        current_utc_date = datetime.now(timezone.utc).strftime('%Y-%m-%d')

        # Clean up daily volume alerts from previous days
        self.daily_volume_alerts = {
            alert_key: date_str for alert_key, date_str in self.daily_volume_alerts.items()
            if date_str == current_utc_date
        }

        # Clean up daily price alerts from previous days
        self.daily_price_alerts = {
            alert_key: date_str for alert_key, date_str in self.daily_price_alerts.items()
            if date_str == current_utc_date
        }

        # Clean up news alerts older than 24 hours (news events don't repeat daily)
        news_cleanup_threshold = current_time - 86400  # 24 hours
        self.recent_news_alerts = {
            key: timestamp for key, timestamp in self.recent_news_alerts.items()
            if timestamp > news_cleanup_threshold
        }

        logger.debug(f"Cleaned up old alerts: {len(self.daily_volume_alerts)} volume, {len(self.daily_price_alerts)} price, {len(self.recent_news_alerts)} news")

    async def _trigger_alert(self, alert_type: str, alert_data: Dict[str, Any]):
        for callback in self.alert_callbacks:
            try:
                await callback(alert_type, alert_data)
            except Exception as e:
                logger.error(f"Error in alert callback: {e}")

    @handle_service_errors
    async def get_current_market_session(self) -> Dict[str, Any]:
        current_time = datetime.now(timezone.utc)
        current_hour_minute = current_time.strftime("%H:%M")

        active_sessions = []

        for session_name, session_config in self.market_sessions.items():
            start_time = session_config['start']
            end_time = session_config['end']

            if self._is_time_in_range(current_hour_minute, start_time, end_time):
                active_sessions.append({
                    'name': session_name,
                    'description': session_config['description'],
                    'start': start_time,
                    'end': end_time,
                    'is_high_volume_period': True
                })

        return {
            'current_time': current_hour_minute,
            'active_sessions': active_sessions,
            'is_optimal_trading_time': len(active_sessions) > 0,
            'next_session': self._get_next_session(current_hour_minute)
        }

    def _is_time_in_range(self, current_time: str, start_time: str, end_time: str) -> bool:
        current = datetime.strptime(current_time, "%H:%M").time()
        start = datetime.strptime(start_time, "%H:%M").time()
        end = datetime.strptime(end_time, "%H:%M").time()

        if start <= end:
            return start <= current <= end
        else:
            return current >= start or current <= end

    def _get_next_session(self, current_time: str) -> Optional[Dict[str, Any]]:
        current = datetime.strptime(current_time, "%H:%M").time()

        next_sessions = []
        for session_name, session_config in self.market_sessions.items():
            start = datetime.strptime(session_config['start'], "%H:%M").time()

            if start > current:
                time_diff = datetime.combine(datetime.today(), start) - datetime.combine(datetime.today(), current)
            else:
                time_diff = datetime.combine(datetime.today() + timedelta(days=1), start) - datetime.combine(datetime.today(), current)

            next_sessions.append({
                'name': session_name,
                'description': session_config['description'],
                'start': session_config['start'],
                'time_until': time_diff.total_seconds() / 3600
            })

        if next_sessions:
            return min(next_sessions, key=lambda x: x['time_until'])
        return None

    @handle_service_errors
    @retry_with_backoff(max_retries=2)
    async def analyze_volume_patterns(self) -> Dict[str, Any]:
        cache_key = "volume_patterns_analysis"
        cached_data = self.cache_service.get(cache_key)
        if cached_data:
            return cached_data

        try:
            market_data = await self.market_service.fetch_coingecko_market_data(per_page=self.top_coins_limit)
            top_coins = market_data.get('top_coins', [])

            volume_analysis = {
                'total_market_volume': 0,
                'high_volume_coins': [],
                'volume_distribution': {},
                'market_activity_level': 'normal'
            }

            total_volume = 0
            high_volume_coins = []

            for coin in top_coins[:50]:
                volume_24h = coin.get('total_volume', 0)
                market_cap = coin.get('market_cap', 1)

                if volume_24h > 0 and market_cap > 0:
                    volume_to_mcap_ratio = (volume_24h / market_cap) * 100
                    total_volume += volume_24h

                    if volume_to_mcap_ratio > 20:
                        high_volume_coins.append({
                            'symbol': coin.get('symbol', '').upper(),
                            'name': coin.get('name', ''),
                            'volume_24h': volume_24h,
                            'volume_to_mcap_ratio': volume_to_mcap_ratio,
                            'price_change_24h': coin.get('price_change_percentage_24h', 0)
                        })

            volume_analysis['total_market_volume'] = total_volume
            volume_analysis['high_volume_coins'] = sorted(high_volume_coins, key=lambda x: x['volume_to_mcap_ratio'], reverse=True)[:10]

            if total_volume > 100_000_000_000:
                volume_analysis['market_activity_level'] = 'high'
            elif total_volume < 50_000_000_000:
                volume_analysis['market_activity_level'] = 'low'

            self.cache_service.set(cache_key, volume_analysis, ttl=300)
            return volume_analysis

        except Exception as e:
            logger.error(f"Error analyzing volume patterns: {e}")
            return {
                'total_market_volume': 0,
                'high_volume_coins': [],
                'volume_distribution': {},
                'market_activity_level': 'unknown'
            }

    @handle_service_errors
    async def detect_volume_surges(self) -> List[Dict[str, Any]]:
        try:
            # Periodically clean up old alert records (every ~100 calls)
            if len(self.daily_volume_alerts) + len(self.daily_price_alerts) > 100:
                self._cleanup_old_alerts()

            market_data = await self.market_service.fetch_coingecko_market_data(per_page=self.top_coins_limit)
            top_coins = market_data.get('top_coins', [])

            volume_surges = []
            current_time = int(time.time())

            for coin in top_coins:
                symbol = coin.get('symbol', '').upper()
                current_volume = coin.get('total_volume', 0)

                if symbol and current_volume > 0:
                    previous_volumes = self.volume_history[symbol]

                    if len(previous_volumes) >= 3:
                        avg_previous_volume = sum(previous_volumes) / len(previous_volumes)

                        if avg_previous_volume > 0:
                            # Use unified percentage calculation service
                            from services.market.percentage_calculation_service import get_percentage_service
                            percentage_service = get_percentage_service()

                            result = percentage_service.calculate_percentage_change(current_volume, avg_previous_volume)
                            volume_increase_pct = result.value if result.is_valid else 0.0

                            for threshold in sorted(self.alert_thresholds.get('volume_surge_levels', [200, 500, 1000]), reverse=True):
                                if volume_increase_pct >= threshold:
                                    # Phase 1: Apply spam protection before creating surge data
                                    if self._should_trigger_volume_alert(symbol, threshold):
                                        surge_data = {
                                            'symbol': symbol,
                                            'name': coin.get('name', ''),
                                            'current_volume': current_volume,
                                            'average_volume': avg_previous_volume,
                                            'volume_increase_pct': volume_increase_pct,
                                            'threshold': threshold,
                                            'price_change_24h': coin.get('price_change_percentage_24h', 0),
                                            'market_cap_rank': coin.get('market_cap_rank', 0),
                                            'timestamp': current_time
                                        }
                                        volume_surges.append(surge_data)
                                        logger.info(f"Volume surge alert triggered: {symbol} +{volume_increase_pct:.1f}% (threshold: {threshold}%)")
                                    break

                    self.volume_history[symbol].append(current_volume)

            if volume_surges:
                await self._trigger_alert('volume_surge', {'surges': volume_surges})

            return volume_surges

        except Exception as e:
            logger.error(f"Error detecting volume surges: {e}")
            return []

    @handle_service_errors
    async def detect_price_movements(self) -> List[Dict[str, Any]]:
        """Detect significant price movements and trigger alerts"""
        try:
            market_data = await self.market_service.fetch_coingecko_market_data(per_page=self.top_coins_limit)
            top_coins = market_data.get('top_coins', [])

            price_movements = []
            current_time = int(time.time())

            for coin in top_coins:
                symbol = coin.get('symbol', '').upper()
                price_change_24h = coin.get('price_change_percentage_24h', 0)

                if symbol and abs(price_change_24h) > 0:
                    # Check each threshold in descending order
                    for threshold in sorted(self.alert_thresholds.get('price_movement_levels', [5, 10, 15]), reverse=True):
                        if abs(price_change_24h) >= threshold:
                            # Apply daily cooldown before creating movement data
                            if self._should_trigger_price_alert(symbol, threshold):
                                movement_data = {
                                    'symbol': symbol,
                                    'name': coin.get('name', ''),
                                    'price_change_24h': price_change_24h,
                                    'threshold': threshold,
                                    'current_price': coin.get('current_price', 0),
                                    'market_cap_rank': coin.get('market_cap_rank', 0),
                                    'timestamp': current_time,
                                    'direction': 'up' if price_change_24h > 0 else 'down'
                                }
                                price_movements.append(movement_data)
                                logger.info(f"Price movement alert triggered: {symbol} {price_change_24h:+.1f}% (threshold: {threshold}%)")
                            break

            if price_movements:
                await self._trigger_alert('price_movement', {'movements': price_movements})

            return price_movements

        except Exception as e:
            logger.error(f"Error detecting price movements: {e}")
            return []

    @handle_service_errors
    async def get_news_trading_opportunities(self) -> List[Dict[str, Any]]:
        try:
            if not self.economic_calendar:
                from services.market.economic_calendar_service import get_economic_calendar_service
                self.economic_calendar = get_economic_calendar_service()

            events = await self.economic_calendar.fetch_economic_calendar()

            opportunities = []
            current_time = datetime.now(timezone.utc)

            for event in events:
                event_time_str = event.get('time', '')
                if event_time_str:
                    try:
                        event_time = datetime.fromisoformat(event_time_str.replace('Z', '+00:00'))
                        time_until_event = (event_time - current_time).total_seconds() / 60

                        if 0 <= time_until_event <= 60:
                            event_title = event.get('title', '')
                            currency = event.get('currency', '')

                            # Phase 1: Apply news event deduplication
                            if self._should_trigger_news_alert(event_title, currency):
                                opportunities.append({
                                    'title': event_title,
                                    'currency': currency,
                                    'impact': event.get('impact', ''),
                                    'time_until_minutes': int(time_until_event),
                                    'event_time': event_time_str,
                                    'trading_recommendation': self._get_trading_recommendation(event, time_until_event)
                                })
                                logger.info(f"News trading opportunity triggered: {event_title} for {currency} in {int(time_until_event)} minutes")
                    except Exception as e:
                        logger.error(f"Error parsing event time: {e}")
                        continue

            if opportunities:
                await self._trigger_alert('news_opportunity', {'opportunities': opportunities})

            return opportunities

        except Exception as e:
            logger.error(f"Error getting news trading opportunities: {e}")
            return []

    def _get_trading_recommendation(self, event: Dict[str, Any], time_until_minutes: float) -> str:
        impact = event.get('impact', '').lower()
        currency = event.get('currency', '').upper()

        if impact == 'high' and currency in ['USD', 'EUR', 'JPY']:
            if time_until_minutes <= 30:
                return "⚠️ High volatility expected - Consider reducing position sizes"
            else:
                return "📈 Prepare for potential trading opportunities"
        elif impact == 'medium':
            return "📊 Monitor price action around event time"
        else:
            return "ℹ️ Low impact expected"

_trading_time_service = None
_instance_lock = threading.RLock()

def get_trading_time_service() -> TradingTimeService:
    global _trading_time_service
    with _instance_lock:
        if _trading_time_service is None:
            _trading_time_service = TradingTimeService()
        return _trading_time_service
