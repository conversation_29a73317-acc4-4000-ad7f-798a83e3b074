"""
Unified Percentage Calculation Service
Provides consistent percentage calculation methods across the entire system
"""

import logging
from typing import Dict, Any, Optional, Tuple
from enum import Enum
from dataclasses import dataclass

logger = logging.getLogger(__name__)

class CalculationMethod(Enum):
    """Enumeration of different percentage calculation methods"""
    ROLLING_24H = "rolling_24h"          # (current - price_24h_ago) / price_24h_ago * 100
    DAILY_OPEN = "daily_open"            # (current - daily_open) / daily_open * 100
    CUSTOM_PERIOD = "custom_period"      # (current - price_at_time) / price_at_time * 100

class DataSource(Enum):
    """Enumeration of data sources"""
    BINANCE_FUTURES = "binance_futures"
    BINANCE_SPOT = "binance_spot"
    COINGECKO = "coingecko"
    MANUAL_CALCULATION = "manual_calculation"

@dataclass
class PercentageResult:
    """Result of percentage calculation"""
    value: float
    method: CalculationMethod
    source: DataSource
    timestamp: int
    base_price: float
    current_price: float
    is_valid: bool = True
    error_message: Optional[str] = None

class PercentageCalculationService:
    """
    Unified service for all percentage calculations in the system
    Ensures consistency across watchlist, alerts, and other features
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Default configuration
        self.default_method = CalculationMethod.ROLLING_24H
        self.default_source = DataSource.BINANCE_FUTURES
        
        # Precision settings
        self.decimal_places = 2
        self.min_base_price = 0.00000001  # Minimum price to avoid division by zero
        
    def calculate_percentage_change(
        self, 
        current_price: float, 
        base_price: float,
        method: CalculationMethod = None,
        source: DataSource = None
    ) -> PercentageResult:
        """
        Calculate percentage change using specified method
        
        Args:
            current_price: Current price value
            base_price: Base price for comparison
            method: Calculation method to use
            source: Data source identifier
            
        Returns:
            PercentageResult with calculation details
        """
        if method is None:
            method = self.default_method
        if source is None:
            source = self.default_source
            
        try:
            # Validate inputs
            if not self._validate_prices(current_price, base_price):
                return PercentageResult(
                    value=0.0,
                    method=method,
                    source=source,
                    timestamp=0,
                    base_price=base_price,
                    current_price=current_price,
                    is_valid=False,
                    error_message="Invalid price values"
                )
            
            # Calculate percentage
            percentage = ((current_price - base_price) / base_price) * 100
            
            # Round to specified decimal places
            percentage = round(percentage, self.decimal_places)
            
            return PercentageResult(
                value=percentage,
                method=method,
                source=source,
                timestamp=int(__import__('time').time()),
                base_price=base_price,
                current_price=current_price,
                is_valid=True
            )
            
        except Exception as e:
            self.logger.error(f"Error calculating percentage change: {e}")
            return PercentageResult(
                value=0.0,
                method=method,
                source=source,
                timestamp=0,
                base_price=base_price,
                current_price=current_price,
                is_valid=False,
                error_message=str(e)
            )
    
    def extract_binance_percentage(self, ticker_data: Dict[str, Any]) -> PercentageResult:
        """
        Extract percentage from Binance ticker data (standardized method)

        Args:
            ticker_data: Binance ticker response

        Returns:
            PercentageResult with Binance data
        """
        try:
            # Validate ticker data structure
            if not isinstance(ticker_data, dict) or 'percentage' not in ticker_data:
                return PercentageResult(
                    value=0.0,
                    method=CalculationMethod.ROLLING_24H,
                    source=DataSource.BINANCE_FUTURES,
                    timestamp=0,
                    base_price=0.0,
                    current_price=0.0,
                    is_valid=False,
                    error_message="Invalid ticker data structure"
                )

            percentage = ticker_data.get('percentage', 0.0) or 0.0
            current_price = ticker_data.get('last', 0.0) or ticker_data.get('close', 0.0)
            open_price = ticker_data.get('open', 0.0)

            return PercentageResult(
                value=round(float(percentage), self.decimal_places),
                method=CalculationMethod.ROLLING_24H,
                source=DataSource.BINANCE_FUTURES,
                timestamp=int(__import__('time').time()),
                base_price=open_price,
                current_price=current_price,
                is_valid=True
            )
            
        except Exception as e:
            self.logger.error(f"Error extracting Binance percentage: {e}")
            return PercentageResult(
                value=0.0,
                method=CalculationMethod.ROLLING_24H,
                source=DataSource.BINANCE_FUTURES,
                timestamp=0,
                base_price=0.0,
                current_price=0.0,
                is_valid=False,
                error_message=str(e)
            )
    
    def extract_coingecko_percentage(self, coin_data: Dict[str, Any]) -> PercentageResult:
        """
        Extract percentage from CoinGecko data (standardized method)
        
        Args:
            coin_data: CoinGecko coin response
            
        Returns:
            PercentageResult with CoinGecko data
        """
        try:
            percentage = coin_data.get('price_change_percentage_24h', 0.0) or 0.0
            current_price = coin_data.get('current_price', 0.0)
            
            return PercentageResult(
                value=round(float(percentage), self.decimal_places),
                method=CalculationMethod.ROLLING_24H,
                source=DataSource.COINGECKO,
                timestamp=int(__import__('time').time()),
                base_price=0.0,  # CoinGecko doesn't provide base price
                current_price=current_price,
                is_valid=True
            )
            
        except Exception as e:
            self.logger.error(f"Error extracting CoinGecko percentage: {e}")
            return PercentageResult(
                value=0.0,
                method=CalculationMethod.ROLLING_24H,
                source=DataSource.COINGECKO,
                timestamp=0,
                base_price=0.0,
                current_price=0.0,
                is_valid=False,
                error_message=str(e)
            )
    
    def format_percentage_display(
        self, 
        result: PercentageResult, 
        include_sign: bool = True,
        include_emoji: bool = True
    ) -> str:
        """
        Format percentage for display with consistent styling
        
        Args:
            result: PercentageResult to format
            include_sign: Whether to include + sign for positive values
            include_emoji: Whether to include emoji indicators
            
        Returns:
            Formatted percentage string
        """
        if not result.is_valid:
            return "N/A"
        
        percentage = result.value
        
        # Determine emoji and sign
        if percentage > 0:
            emoji = "🟢" if include_emoji else ""
            sign = "+" if include_sign else ""
        elif percentage < 0:
            emoji = "🔴" if include_emoji else ""
            sign = ""
        else:
            emoji = "⚪" if include_emoji else ""
            sign = ""
        
        # Format with consistent decimal places
        formatted = f"{sign}{percentage:.{self.decimal_places}f}%"
        
        if include_emoji:
            return f"{emoji}{formatted}"
        else:
            return formatted
    
    def _validate_prices(self, current_price: float, base_price: float) -> bool:
        """
        Validate price inputs for calculation

        Args:
            current_price: Current price to validate
            base_price: Base price to validate

        Returns:
            True if prices are valid for calculation
        """
        try:
            # Check if prices are numbers
            if not isinstance(current_price, (int, float)) or not isinstance(base_price, (int, float)):
                return False

            # Check for zero or negative base price (cannot divide by zero or negative)
            if base_price <= 0:
                return False

            # Check if base price is too small (avoid division by very small numbers)
            if abs(base_price) < self.min_base_price:
                return False

            # Check for negative current price
            if current_price < 0:
                return False

            return True

        except Exception:
            return False

# Global service instance
_percentage_service = None

def get_percentage_service() -> PercentageCalculationService:
    """Get global percentage calculation service instance"""
    global _percentage_service
    if _percentage_service is None:
        _percentage_service = PercentageCalculationService()
    return _percentage_service
